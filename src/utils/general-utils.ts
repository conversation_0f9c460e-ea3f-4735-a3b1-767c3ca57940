export enum HistoryPropertyType {
  DATE = 'date',
  CURRENCY = 'currency',
  BOOLEAN = 'boolean',
  STRING = 'string',
}

export function getHistoryPropertyType(fieldKey: string): HistoryPropertyType {
  const dateFields = [
    'scheduledDeliveryTime',
    'scheduledCollectionTime',
    'actualCollectionTime',
    'actualDeliveryTime',
    'createdAt',
    'codCollectionDate',
  ];

  const currencyFields = [
    'declaredValue',
    'codAmount',
    'basePrice',
    'optionsPrice',
    'miscAdjustment',
    'customerAdjustment',
    'totalPrice',
  ];

  const booleanFields = [
    'collectionSignatureRequired',
    'deliverySignatureRequired',
    'codCollected',
    'isCod',
    'isInsurance',
  ];

  if (dateFields.includes(fieldKey)) {
    return HistoryPropertyType.DATE;
  }

  if (currencyFields.includes(fieldKey)) {
    return HistoryPropertyType.CURRENCY;
  }

  if (booleanFields.includes(fieldKey)) {
    return HistoryPropertyType.BOOLEAN;
  }

  return HistoryPropertyType.STRING;
}
