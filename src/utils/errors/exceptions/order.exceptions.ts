import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { OrderStatus } from '@app/business/order/orders/domain/order.types';

export class OrderNotFoundException extends AppException {
  constructor(id: string) {
    super(
      `Order not found with ID: ${id}`,
      ErrorCode.ORDER_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class OrderTenantMismatchException extends AppException {
  constructor(orderId: string, tenantId: string) {
    super(
      `Order with ID: ${orderId} does not belong to tenant: ${tenantId}`,
      ErrorCode.UNAUTHORIZED,
      HttpStatus.FORBIDDEN,
      {
        orderId,
        tenantId,
        errorType: 'TENANT_MISMATCH',
      },
    );
  }
}

export class OrderLockedException extends AppException {
  constructor(orderId: string, lockedBy: string) {
    super(
      `Order with ID: ${orderId} is currently locked`,
      ErrorCode.ORDER_LOCKED,
      HttpStatus.CONFLICT,
      {
        orderId,
        lockedBy,
        errorType: 'ORDER_LOCKED',
      },
    );
  }
}

export class InvalidOrderStatusTransitionException extends AppException {
  constructor(
    orderId: string,
    currentStatus: OrderStatus,
    newStatus: OrderStatus,
  ) {
    super(
      `Cannot transition order from ${currentStatus} to ${newStatus}`,
      ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        orderId,
        currentStatus,
        newStatus,
        errorType: 'INVALID_STATUS_TRANSITION',
      },
    );
  }
}

export class OrderAlreadyAssignedException extends AppException {
  constructor(orderId: string, assigneeId: string) {
    super(
      `Order is already assigned to driver`,
      ErrorCode.ORDER_ALREADY_ASSIGNED,
      HttpStatus.CONFLICT,
      {
        orderId,
        assigneeId,
        errorType: 'ALREADY_ASSIGNED',
      },
    );
  }
}

export class OrderNotAssignedException extends AppException {
  constructor(orderId: string) {
    super(
      `Order is not currently assigned to any driver`,
      ErrorCode.ORDER_NOT_ASSIGNED_TO_DRIVER,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        orderId,
        errorType: 'NOT_ASSIGNED',
      },
    );
  }
}

export class OrderUpdateFailedException extends AppException {
  constructor(orderId: string, reason?: string) {
    super(
      `Failed to update order${reason ? `: ${reason}` : ''}`,
      ErrorCode.ORDER_UPDATE_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      {
        orderId,
        reason,
      },
    );
  }
}

export class OrderUpdateBadRequestException extends AppException {
  constructor(orderId: string, reason?: string) {
    super(
      `Failed to update order${reason ? `: ${reason}` : ''}`,
      ErrorCode.ORDER_UPDATE_FAILED,
      HttpStatus.BAD_REQUEST,
      {
        orderId,
        reason,
      },
    );
  }
}

export class OrderCreationFailedException extends AppException {
  constructor(reason?: string) {
    super(
      `Failed to create order${reason ? `: ${reason}` : ''}`,
      ErrorCode.ORDER_CREATION_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      {
        reason,
      },
    );
  }
}

export class InvalidOrderDataException extends AppException {
  constructor(message: string, field?: string) {
    super(message, ErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, {
      field,
      errorType: 'INVALID_ORDER_DATA',
    });
  }
}

export class OrderCompletionRequirementException extends AppException {
  constructor(orderId: string, requirement: string) {
    super(
      `Cannot complete order: ${requirement} is required`,
      ErrorCode.ORDER_COMPLETION_REQUIREMENT,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        orderId,
        requirement,
        errorType: 'COMPLETION_REQUIREMENT',
      },
    );
  }
}

export class OrderItemNotFoundException extends AppException {
  constructor(id: string) {
    super(
      `Order item not found with ID: ${id}`,
      ErrorCode.ORDER_ITEM_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class OrderDeliveryTimeInvalidException extends AppException {
  constructor() {
    super(
      'Delivery time must be after collection time',
      ErrorCode.ORDER_DELIVERY_TIME_INVALID,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        errorType: 'INVALID_DELIVERY_TIME',
      },
    );
  }
}

export class CustomModifierAlreadyExistsException extends AppException {
  constructor(name: string, orderId: string) {
    super(
      `Custom pricing modifier with name "${name}" already exists for this order`,
      ErrorCode.CUSTOM_MODIFIER_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        name,
        orderId,
        constraint: 'unique_modifier_name_per_order',
      },
    );
  }
}

export class CustomModifierNotFoundException extends AppException {
  constructor(id: string) {
    super(
      `Custom modifier not found with ID: ${id}`,
      ErrorCode.ORDER_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}
