import { Injectable, Logger } from '@nestjs/common';
import {
  IRange,
  ITieredRange,
  RangeOperator,
} from '../../domain/price-modifier.interface';

@Injectable()
export class CalculationUtilsService {
  private readonly logger = new Logger(CalculationUtilsService.name);

  /**
   * Evaluates if a value meets a condition defined by an operator and comparison value
   */

  evaluateCondition(
    value: number,
    comparisonValue: number | string,
    operator: RangeOperator,
  ): boolean {
    const compareVal =
      typeof comparisonValue === 'string'
        ? parseFloat(comparisonValue)
        : comparisonValue;

    switch (operator) {
      case RangeOperator.GREATER_THAN:
        return value > compareVal;
      case RangeOperator.GREATER_THAN_OR_EQUAL:
        return value >= compareVal;
      case RangeOperator.LESS_THAN:
        return value < compareVal;
      case RangeOperator.LESS_THAN_OR_EQUAL:
        return value <= compareVal;
      default:
        this.logger.warn(`Unknown operator: ${operator}`);
        return false;
    }
  }

  /**
   * Checks if a value is within a defined range
   */
  isInRange(value: number, range: IRange): boolean {
    if (!range) return true;

    let isInRange = true;

    if (range.from) {
      isInRange =
        isInRange &&
        this.evaluateCondition(value, range.from.value, range.from.operator);
    }

    if (range.to) {
      isInRange =
        isInRange &&
        this.evaluateCondition(value, range.to.value, range.to.operator);
    }

    return isInRange;
  }

  /**
   * Finds the appropriate value from tiered ranges based on a target value
   */
  evaluateTieredRange(
    targetValue: number,
    tieredRanges: ITieredRange[],
  ): number {
    if (!tieredRanges || tieredRanges.length === 0) {
      return 0;
    }

    for (const range of tieredRanges) {
      if (this.isInRange(targetValue, range)) {
        return range.value;
      }
    }

    return 0;
  }
}
