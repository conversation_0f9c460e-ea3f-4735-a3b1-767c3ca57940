import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
  ITieredRange,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.TIERED_INCREMENTAL_OVERAGE_PERCENTAGE)
@Injectable()
export class TieredIncrementalOveragePercentageStrategy extends BaseCalculationStrategy {
  private calculationValue: number = 0;
  private basePrice: number = 0;
  private increment: number = 1;
  private tieredRanges: ITieredRange[] = [];
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for TieredIncrementalOveragePercentage: ${modifier.id}`,
    );

    // Get the value from the specified field
    this.calculationValue = this.getFieldValue(
      order,
      modifier.calculationField,
    );

    // Store the base price
    this.basePrice = order.basePrice;

    // Set the tiered ranges
    this.tieredRanges = modifier.tieredRanges || [];

    // Set the increment value
    this.increment = Number(modifier.increment) || 1;

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    // Set applicable range if present
    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `TieredIncrementalOveragePercentage parameters set: calculationValue=${this.calculationValue}, ` +
        `basePrice=${this.basePrice}, increment=${this.increment}, ` +
        `tiers=${this.tieredRanges.length}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'TieredIncrementalOveragePercentage calculation skipped: modifier disabled',
      );
      return 0;
    }

    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(
          this.calculationValue,
          this.applicableRange,
        )
      ) {
        this.logger.debug(
          'TieredFixedOverageAmount calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    if (!this.tieredRanges || this.tieredRanges.length === 0) {
      this.logger.debug(
        'TieredIncrementalOveragePercentage calculation skipped: no tiered ranges defined',
      );
      return 0;
    }

    // Evaluate which tier the value falls into and get the corresponding percentage
    const percentagePerIncrement = this.calculationUtils.evaluateTieredRange(
      this.calculationValue,
      this.tieredRanges,
    );
    if (percentagePerIncrement <= 0) {
      this.logger.debug(
        'TieredIncrementalOveragePercentage calculation skipped: no matching tier or zero percentage',
      );
      return 0;
    }

    // Calculate number of increments
    const numberOfIncrements = Math.ceil(
      this.calculationValue / this.increment,
    );

    // Calculate total percentage
    const totalPercentage = percentagePerIncrement * numberOfIncrements;

    // Calculate the final amount
    const result = (this.basePrice * totalPercentage) / 100;

    this.logger.debug(
      `TieredIncrementalOveragePercentage calculation: ` +
        `${numberOfIncrements} increments at ${percentagePerIncrement}% each, ` +
        `${totalPercentage}% total, ` +
        `result=${result}`,
    );

    return result;
  }
}
