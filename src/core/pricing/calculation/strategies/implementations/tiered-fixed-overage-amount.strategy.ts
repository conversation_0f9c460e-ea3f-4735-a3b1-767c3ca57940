import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
  ITieredRange,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.TIERED_FIXED_OVERAGE_AMOUNT)
@Injectable()
export class TieredFixedOverageAmountStrategy extends BaseCalculationStrategy {
  private calculationValue: number = 0;
  private tieredRanges: ITieredRange[] = [];
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for TieredFixedOverageAmount: ${modifier.id}`,
    );

    // Get the value from the specified field
    this.calculationValue = this.getFieldValue(
      order,
      modifier.calculationField,
    );

    // Set the tiered ranges
    this.tieredRanges = modifier.tieredRanges || [];

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `TieredFixedOverageAmount parameters set: calculationValue=${this.calculationValue}, ` +
        `tiers=${this.tieredRanges.length}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'TieredFixedOverageAmount calculation skipped: modifier disabled',
      );
      return 0;
    }

    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(
          this.calculationValue,
          this.applicableRange,
        )
      ) {
        this.logger.debug(
          'TieredFixedOverageAmount calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    if (!this.tieredRanges || this.tieredRanges.length === 0) {
      this.logger.debug(
        'TieredFixedOverageAmount calculation skipped: no tiered ranges defined',
      );
      return 0;
    }

    // Evaluate which tier the value falls into and get the corresponding amount
    const result = this.calculationUtils.evaluateTieredRange(
      this.calculationValue,
      this.tieredRanges,
    );

    this.logger.debug(`TieredFixedOverageAmount calculation result: ${result}`);
    return result;
  }
}
