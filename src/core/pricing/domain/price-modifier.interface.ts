import { ConfigurationType } from "../../../business/pricing/price-sets/domain/price-set.types";

export enum CalculationType {
  FLAT_AMOUNT = 'FlatAmount',
  FLAT_PERCENTAGE = 'FlatPercentage',
  FLAT_OVERAGE_AMOUNT = 'FlatOverageAmount',
  FLAT_OVERAGE_PERCENTAGE = 'FlatOveragePercentage',
  INCREMENTAL_OVERAGE_AMOUNT = 'IncrementalOverageAmount',
  INCREMENTAL_OVERAGE_PERCENTAGE = 'IncrementalOveragePercentage',
  TIERED_FIXED_OVERAGE_AMOUNT = 'TieredFixedOverageAmount',
  TIERED_FIXED_OVERAGE_PERCENTAGE = 'TieredFixedOveragePercentage',
  TIERED_INCREMENTAL_OVERAGE_AMOUNT = 'TieredIncrementalOverageAmount',
  TIERED_INCREMENTAL_OVERAGE_PERCENTAGE = 'TieredIncrementalOveragePercentage',
}

export enum CalculationField {
  BASE_PRICE = 'BasePrice',
  DECLARED_PRICE = 'DeclaredPrice',
  WEIGHT = 'Weight',
  DISTANCE = 'Distance',
  QUANTITY = 'Quantity',
  // Dimensional fields
  HEIGHT = 'Height',
  WIDTH = 'Width',
  LENGTH = 'Length',
  CUBIC_DIMENSIONS = 'CubicDimensions',
  // Value fields
  CUSTOM_AMOUNT = 'CustomAmount',
  // Time fields (in minutes)
  COLLECTION_WAIT_TIME = 'CollectionWaitTime',
  DELIVERY_WAIT_TIME = 'DeliveryWaitTime',
}

export enum RangeOperator {
  GREATER_THAN = 'GreaterThan',
  GREATER_THAN_OR_EQUAL = 'GreaterThanOrEqual',
  LESS_THAN = 'LessThan',
  LESS_THAN_OR_EQUAL = 'LessThanOrEqual',
}

export interface IRange {
  from?: {
    value: number;
    operator: RangeOperator;
  };
  to?: {
    value: number;
    operator: RangeOperator;
  };
}

export interface ITieredRange extends IRange {
  value: number;
}

export interface IPriceModifier {
  id: string;
  name: string;
  calculationType: CalculationType;
  calculationField: CalculationField;
  configuration? : ConfigurationType;
  value: number;
  increment?: number;
  calculationStartAfter?: number;
  applicableRange?: IRange;
  tieredRanges?: ITieredRange[];
  isGroupModifier: boolean;
  modifiers?: IPriceModifier[];
  groupBehavior?: GroupBehavior;
  isEnabled: boolean;
  children?: any;
}

export enum GroupBehavior {
  USE_SUM = 'UseSum',
  USE_HIGHEST = 'UseHighest',
  USE_LOWEST = 'UseLowest',
}
