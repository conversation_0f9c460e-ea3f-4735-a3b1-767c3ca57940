import { CalculationType } from '@core/pricing/domain/price-modifier.interface';

export interface IModifierResult {
  id: string;
  name: string;
  type: CalculationType;
  amount: number;
  isGroupResult: boolean;
  configuration?: any
  children?: IModifierResult[];
}

export interface ICalculationResult {
  orderId: string;
  basePrice: number;
  modifiers: IModifierResult[];
  totalPrice: number;
  totalModifierPrice?: number;
  errors: string[];
}
