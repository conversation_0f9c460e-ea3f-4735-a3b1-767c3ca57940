import { Injectable, BadRequestException } from '@nestjs/common';
import {
  SelectQueryBuilder,
  Brackets,
  ObjectLiteral,
  WhereExpressionBuilder,
} from 'typeorm';
import {
  FilterableField,
  FilterConfig,
  TransformedValue,
} from '../config/filter-config';
import {
  FilterOperator,
  FilterCondition,
  LogicalOperator,
  isLogicalOperator,
  operatorMapping,
} from '../types/filter.types';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { BaseFilterDto } from '../dtos/base-filter.dto';
import { ParsedQs } from 'qs';

@Injectable()
export class SecureFilterService {
  constructor(private readonly filterConfig: FilterConfig) {}

  /**
   * Parse query parameters in format field:operator=value into structured filter object
   * @returns BaseFilterDto with populated values
   * @param query
   * @param filter
   */
  parseKeyOperatorValueQuery(
    query: ParsedQs,
    filter: BaseFilterDto,
  ): BaseFilterDto {
    filter.where = filter.where || {};

    Object.entries(query).forEach(([key, value]) => {
      if (!key.includes(':')) return;

      const [field, operatorKey] = key.split(':');

      // Skip standard parameters
      if (
        [
          'sortField',
          'sortDirection',
          'pageNumber',
          'pageSize',
          'searchTerm',
        ].includes(field)
      ) {
        return;
      }

      // Map operator to internal enum
      const operator = this.mapOperator(operatorKey);
      if (!operator) {
        throw new BadRequestException(`Unsupported operator: ${operatorKey}`);
      }

      const fieldConfig = this.filterConfig.fields[field];
      if (!fieldConfig) {
        throw new BadRequestException(
          `Filtering not allowed on field: ${field}`,
        );
      }

      // Parse the value based on operator and field config
      const parsedValue = this.parseQueryValue(
        operator,
        value as string,
        fieldConfig,
      );

      // Initialize the field in where clause if it doesn't exist
      if (
        typeof filter.where === 'object' &&
        !isLogicalOperator(filter.where)
      ) {
        filter.where[field] = filter.where[field] || {};
        filter.where[field][operator] = parsedValue;
      }
    });

    return filter;
  }

  /**
   * Build a TypeORM query based on the provided filter
   */
  async buildQuery<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    filter: BaseFilterDto,
  ): Promise<SelectQueryBuilder<T>> {
    const alias = queryBuilder.alias;
    // Apply where conditions
    if (filter.where) {
      await this.validateWhereConditions(filter.where);
      await this.applyWhereConditions(queryBuilder, filter.where, alias);
    }

    // Apply search
    if (filter.searchTerm) {
      this.applySearch(queryBuilder, filter.searchTerm, alias);
    }

    // Apply ordering
    this.applyOrdering(queryBuilder, filter, alias);

    // Apply pagination
    this.applyPagination(queryBuilder, filter);

    return queryBuilder;
  }

  /**
   * Execute the query and return paginated results
   */
  async executeQuery<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<T>> {
    const [data, total] = await queryBuilder.getManyAndCount();
    const page = filter.pageNumber || 1;
    const limit = filter.pageSize || this.filterConfig.defaultTake || 10;
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  /**
   * Validate the where conditions
   */
  private async validateWhereConditions(
    where: FilterCondition | LogicalOperator,
  ): Promise<void> {
    if (isLogicalOperator(where)) {
      const conditions = [...(where.AND || []), ...(where.OR || [])];

      await Promise.all(
        conditions.map((condition) => this.validateWhereConditions(condition)),
      );
    } else {
      await Promise.all(
        Object.entries(where).map(async ([field, operators]) => {
          const fieldConfig = this.filterConfig.fields[field];

          if (!fieldConfig) {
            throw new BadRequestException(
              `Filtering not allowed on field: ${field}`,
            );
          }

          await Promise.all(
            Object.entries(operators).map(async ([operator, value]) => {
              if (
                !fieldConfig.operators ||
                !fieldConfig.operators.includes(operator as FilterOperator)
              ) {
                throw new BadRequestException(
                  `Operator '${operator}' not allowed on field: ${field}`,
                );
              }

              if (fieldConfig.validate) {
                const isValid = await fieldConfig.validate(value);

                if (!isValid) {
                  throw new BadRequestException(
                    fieldConfig.validationMessage ||
                      `Invalid value for field: ${field}`,
                  );
                }
              }
            }),
          );
        }),
      );
    }
  }

  /**
   * Apply where conditions to the query
   */
  private async applyWhereConditions<T extends ObjectLiteral>(
    queryBuilder: WhereExpressionBuilder,
    where: FilterCondition | LogicalOperator,
    alias: string,
  ): Promise<void> {
    if (isLogicalOperator(where)) {
      // Handle AND/OR conditions
      queryBuilder.andWhere(
        new Brackets(async (qb) => {
          await this.applyLogicalOperator(qb, where, alias);
        }),
      );
    } else {
      await Promise.all(
        Object.entries(where).map(async ([field, operators]) => {
          const fieldConfig = this.filterConfig.fields[field];

          // Handle related fields
          if (fieldConfig?.relation && fieldConfig?.relationField) {
            const relationAlias = fieldConfig.relation;

            // Ensure relation is joined if using SelectQueryBuilder
            if (queryBuilder instanceof SelectQueryBuilder) {
              if (
                !queryBuilder.expressionMap.joinAttributes.some(
                  (join) => join.alias.name === relationAlias,
                )
              ) {
                queryBuilder.leftJoin(
                  `${alias}.${fieldConfig.relation}`,
                  relationAlias,
                );
              }
            }

            // Apply operators
            await Promise.all(
              Object.entries(operators).map(async ([operator, value]) => {
                const paramName = `${field.replace(/\./g, '_')}_${Date.now()}`;
                const columnName = `${relationAlias}.${fieldConfig.relationField}`;
                const whereCondition = this.buildWhereCondition(
                  columnName,
                  operator as FilterOperator,
                  paramName,
                );
                queryBuilder.andWhere(whereCondition, { [paramName]: value });
              }),
            );
          } else {
            // Handle regular fields
            await Promise.all(
              Object.entries(operators).map(async ([operator, value], ind) => {
                const paramName = `${field.replace(/\./g, '_')}_${ind}`;
                const columnName = `${alias}.${field}`;
                const whereCondition = this.buildWhereCondition(
                  columnName,
                  operator as FilterOperator,
                  paramName,
                );
                queryBuilder.andWhere(whereCondition, { [paramName]: value });
              }),
            );
          }
        }),
      );
    }
  }

  /**
   * Apply search term to the query
   * This method has been completely revised to search across all searchable fields,
   * including both text and number fields, using substring matching for all.
   */
  private applySearch<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    searchTerm: string,
    alias: string,
  ): void {
    const fields = this.filterConfig.fields;
    const searchableFields: Array<{
      field: string;
      relation?: string;
      relationField?: string;
      type?: 'string' | 'number' | 'boolean' | 'date';
    }> = [];

    // First, collect all searchable fields
    Object.entries(fields).forEach(([fieldName, fieldConfig]) => {
      if (fieldConfig.searchable) {
        searchableFields.push({
          field: fieldName,
          relation: fieldConfig.relation,
          relationField: fieldConfig.relationField,
          type: fieldConfig.type,
        });

        // Ensure relation is joined if it exists
        if (fieldConfig.relation) {
          const relationAlias = fieldConfig.relation;
          if (
            !queryBuilder.expressionMap.joinAttributes.some(
              (join) => join.alias.name === relationAlias,
            )
          ) {
            queryBuilder.leftJoin(
              `${alias}.${fieldConfig.relation}`,
              relationAlias,
            );
          }
        }
      }
    });

    if (searchableFields.length === 0) return;

    queryBuilder.andWhere(
      new Brackets((qb) => {
        searchableFields.forEach((field, index) => {
          let columnName: string;

          if (field.relation && field.relationField) {
            columnName = `${field.relation}.${field.relationField}`;
          } else {
            columnName = `${alias}.${field.field}`;
          }

          const paramName = `search_${field.field.replace(/\./g, '_')}`;

          if (index === 0) {
            qb.where(`CAST(${columnName} AS TEXT) ILIKE :${paramName}`, {
              [paramName]: `%${searchTerm}%`,
            });
          } else {
            qb.orWhere(`CAST(${columnName} AS TEXT) ILIKE :${paramName}`, {
              [paramName]: `%${searchTerm}%`,
            });
          }
        });
      }),
    );
  }

  /**
   * Apply ordering to the query
   */
  private applyOrdering<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    filter: BaseFilterDto,
    alias: string,
  ): void {
    if (filter.sortDirection && filter.sortField) {
      const orderBy = {
        [filter.sortField]: filter.sortDirection as 'ASC' | 'DESC',
      };
      this.validateOrderBy(orderBy);

      for (const [field, direction] of Object.entries(orderBy)) {
        queryBuilder.addOrderBy(`${alias}.${field}`, direction);
      }
    } else if (this.filterConfig.defaultSort) {
      const { field, direction } = this.filterConfig.defaultSort;
      queryBuilder.addOrderBy(`${alias}.${field}`, direction);
    }
  }

  /**
   * Apply pagination to the query
   */
  private applyPagination<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    filter: BaseFilterDto,
  ): void {
    const take = Math.min(
      filter.pageSize || this.filterConfig.defaultTake || 10,
      this.filterConfig.maxTake || 100,
    );

    queryBuilder.take(take);

    if (filter.pageNumber) {
      const skip = (filter.pageNumber - 1) * take;
      queryBuilder.skip(skip);
    }
  }

  /**
   * Apply logical operators (AND/OR) to the query
   */
  private async applyLogicalOperator(
    queryBuilder: WhereExpressionBuilder,
    operator: LogicalOperator,
    alias: string,
  ): Promise<void> {
    // Handle AND conditions
    if (operator.AND?.length) {
      await Promise.all(
        operator.AND.map((condition) =>
          queryBuilder.andWhere(
            new Brackets((qb) =>
              this.applyWhereConditions(qb, condition, alias),
            ),
          ),
        ),
      );
    }

    // Handle OR conditions
    if (operator.OR?.length) {
      queryBuilder.andWhere(
        new Brackets(async (qb) => {
          for (const [index, condition] of operator.OR!.entries()) {
            if (index === 0) {
              await this.applyWhereConditions(qb, condition, alias);
            } else {
              qb.orWhere(
                new Brackets((subQb) =>
                  this.applyWhereConditions(subQb, condition, alias),
                ),
              );
            }
          }
        }),
      );
    }
  }

  /**
   * Apply filter conditions to the query
   */
  private async applyFilterConditions(
    queryBuilder: WhereExpressionBuilder,
    conditions: FilterCondition,
    alias: string,
  ): Promise<void> {
    await Promise.all(
      Object.entries(conditions).map(async ([field, operators]) => {
        const fieldConfig = this.filterConfig.fields[field];

        // Handle related fields
        const columnAlias = fieldConfig?.relation
          ? fieldConfig.relation
          : alias;

        const columnName = fieldConfig?.relation
          ? `${columnAlias}.${fieldConfig.relationField}`
          : `${alias}.${field}`;

        await Promise.all(
          Object.entries(operators).map(async ([operator, value]) => {
            const paramName = this.generateParamName(
              field,
              operator as FilterOperator,
            );

            const whereCondition = this.buildWhereCondition(
              columnName,
              operator as FilterOperator,
              paramName,
            );

            const parameters =
              operator === FilterOperator.BETWEEN
                ? {
                    [`${paramName}_start`]: await this.formatValue(
                      operator as FilterOperator,
                      value,
                    ).start,
                    [`${paramName}_end`]: await this.formatValue(
                      operator as FilterOperator,
                      value,
                    ).end,
                  }
                : {
                    [paramName]: await this.formatValue(
                      operator as FilterOperator,
                      value,
                    ),
                  };

            queryBuilder.andWhere(whereCondition, parameters);
          }),
        );
      }),
    );
  }

  /**
   * Validate ordering
   */
  private validateOrderBy(orderBy: Record<string, 'ASC' | 'DESC'>): void {
    for (const field of Object.keys(orderBy)) {
      const fieldConfig = this.filterConfig.fields[field];

      if (!fieldConfig?.sortable) {
        throw new BadRequestException(`Sorting not allowed on field: ${field}`);
      }
    }
  }

  /**
   * Generate a unique parameter name
   */
  private generateParamName(field: string, operator: FilterOperator): string {
    return `${field}_${operator}_${Math.random().toString(36).substring(7)}`;
  }

  /**
   * Build a SQL WHERE condition based on the operator
   */
  private buildWhereCondition(
    columnName: string,
    operator: FilterOperator,
    paramName: string,
  ): string {
    switch (operator) {
      case FilterOperator.EQ:
        return `${columnName} = :${paramName}`;
      case FilterOperator.NEQ:
        return `${columnName} != :${paramName}`;
      case FilterOperator.GT:
        return `${columnName} > :${paramName}`;
      case FilterOperator.GTE:
        return `${columnName} >= :${paramName}`;
      case FilterOperator.LT:
        return `${columnName} < :${paramName}`;
      case FilterOperator.LTE:
        return `${columnName} <= :${paramName}`;
      case FilterOperator.LIKE:
        return `${columnName} LIKE :${paramName}`;
      case FilterOperator.ILIKE:
        return `${columnName} ILIKE :${paramName}`;
      case FilterOperator.STARTS_WITH:
        return `${columnName} ILIKE :${paramName}`;
      case FilterOperator.ENDS_WITH:
        return `${columnName} ILIKE :${paramName}`;
      case FilterOperator.CONTAINS:
        return `${columnName} ILIKE :${paramName}`;
      case FilterOperator.NOT_CONTAINS:
        return `${columnName} NOT ILIKE :${paramName}`;
      case FilterOperator.IN:
        return `${columnName} IN (:...${paramName})`;
      case FilterOperator.NOT_IN:
        return `${columnName} NOT IN (:...${paramName})`;
      case FilterOperator.BETWEEN:
        return `${columnName} BETWEEN :${paramName}_start AND :${paramName}_end`;
      default:
        throw new BadRequestException(`Unsupported operator: ${operator}`);
    }
  }

  /**
   * Format filter values based on the operator
   */
  private formatValue(operator: FilterOperator, value: any): any {
    switch (operator) {
      case FilterOperator.LIKE:
      case FilterOperator.ILIKE:
      case FilterOperator.CONTAINS:
        return `%${value}%`;
      case FilterOperator.NOT_CONTAINS:
        return `%${value}%`;
      case FilterOperator.STARTS_WITH:
        return `${value}%`;
      case FilterOperator.ENDS_WITH:
        return `%${value}`;
      case FilterOperator.BETWEEN:
        if (!Array.isArray(value) || value.length !== 2) {
          throw new BadRequestException(
            'BETWEEN operator requires an array of two values',
          );
        }
        return {
          start: value[0],
          end: value[1],
        };
      case FilterOperator.IS_NULL:
      case FilterOperator.IS_NOT_NULL:
        return null;
      default:
        return value;
    }
  }

  /**
   * Map string operator from URL to FilterOperator enum
   */
  private mapOperator(operatorStr: string): FilterOperator | null {
    return operatorMapping[operatorStr] || null;
  }

  /**
   * Parse query value based on operator
   */
  private parseQueryValue(
    operator: FilterOperator,
    value: string,
    fieldConfig?: FilterableField,
  ): any {
    // First apply field-specific transformation if available
    let transformedValue: TransformedValue = value;

    if (fieldConfig?.transform) {
      transformedValue = fieldConfig.transform(value);
    } else if (fieldConfig?.type) {
      // Default type transformations
      switch (fieldConfig.type) {
        case 'number':
          const numValue = Number(value);
          if (isNaN(numValue)) {
            throw new BadRequestException(`Invalid number format: ${value}`);
          }
          transformedValue = numValue;
          break;
        case 'boolean':
          transformedValue = value.toLowerCase() === 'true';
          break;
        case 'date':
          const dateValue = new Date(value);
          if (isNaN(dateValue.getTime())) {
            throw new BadRequestException(`Invalid date format: ${value}`);
          }
          transformedValue = dateValue;
          break;
      }
    }

    // Then apply operator-specific transformations
    switch (operator) {
      case FilterOperator.STARTS_WITH:
        return `${String(transformedValue)}%`;
      case FilterOperator.ENDS_WITH:
        return `%${String(transformedValue)}`;
      case FilterOperator.CONTAINS:
      case FilterOperator.NOT_CONTAINS:
        return `%${String(transformedValue)}%`;
      case FilterOperator.IN:
      case FilterOperator.NOT_IN:
        const values = value.split(',');
        if (fieldConfig?.type === 'number') {
          return values.map((v) => {
            const num = Number(v);
            if (isNaN(num)) {
              throw new BadRequestException(
                `Invalid number format in array: ${v}`,
              );
            }
            return num;
          });
        }
        if (fieldConfig?.type === 'date') {
          return values.map((v) => {
            const date = new Date(v.trim());
            if (isNaN(date.getTime())) {
              throw new BadRequestException(
                `Invalid date format in array: ${v}`,
              );
            }
            return date;
          });
        }
        if (fieldConfig?.type === 'string') {
          return values.map((v) => v.trim());
        }
        return values;
      case FilterOperator.BETWEEN:
        const [start, end] = value.split(',');
        if (fieldConfig?.type === 'number') {
          const startNum = Number(start);
          const endNum = Number(end);
          if (isNaN(startNum) || isNaN(endNum)) {
            throw new BadRequestException(
              `Invalid number format in range: ${value}`,
            );
          }
          return { start: startNum, end: endNum };
        }
        if (fieldConfig?.type === 'date') {
          const startDate = new Date(start);
          const endDate = new Date(end);
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new BadRequestException(
              `Invalid date format in range: ${value}`,
            );
          }
          return { start: startDate, end: endDate };
        }
        return { start, end };
      default:
        return transformedValue;
    }
  }
}
