export interface OrderPricePreviewDto {
  orderId: string;
  priceSetChanged: boolean;
  changedFields: any[];
  pricing: {
    old: any;
    new: any;
  };
}

export interface ChangedFieldDto {
  field: string;
  oldValue: any;
  newValue: any;
}

export interface PriceSnapshotDto {
  basePrice: number;
  optionsPrice: number;
  totalPrice: number;
  modifiers: ModifierDto[];
  customModifiers?: any;
}

export interface PriceDeltaDto {
  basePriceDelta: number;
  optionsPriceDelta: number;
  totalPriceDelta: number;
  modifierDeltas: ModifierDeltaDto[];
}

export interface ModifierDto {
  id: string;
  name: string;
  amount: number;
}

export interface ModifierDeltaDto {
  id: string;
  name: string;
  oldAmount: number;
  newAmount: number;
  delta: number;
}
