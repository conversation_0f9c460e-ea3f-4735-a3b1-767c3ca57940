import { Injectable } from '@nestjs/common';
import { OrderAssignmentHistoryRepository } from '../infrastructure/repositories/order-assignment-history.repository';
import { OrderRepository } from '../infrastructure/repositories/order.repository';
import { OrderStatus } from '../domain/order.types';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderStatusService } from './order-status.service';
import { HistoryService } from '../../../history/history.service';
import { HISTORY_ENTITIES } from '../../../history/infrastructure/constants/entity.constants';
import { DriverService } from '../../../user/drivers/driver.service';

@Injectable()
export class OrderAssignmentService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly assignmentHistoryRepository: OrderAssignmentHistoryRepository,
    private readonly orderStatusService: OrderStatusService,
    private readonly historyService: HistoryService,
    private readonly driverService: DriverService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Assign an order to a driver and vehicle
   */
  async assignOrder(
    orderId: string,
    driverId: string,
    assignedById: string,
    vehicleId?: string,
    reason?: string,
  ): Promise<boolean> {
    // Get the order
    const order = await this.orderRepository.findById(orderId);
    if (!order) {
      return false;
    }

    // Check if order is locked
    if (order.isLocked) {
      return false;
    }

    // Get the current assignment info for history recording
    const currentAssignee =
      await this.assignmentHistoryRepository.findLatestAssigneeByOrderId(
        orderId,
      );
    const currentVehicle =
      await this.assignmentHistoryRepository.findLatestVehicleByOrderId(
        orderId,
      );

    // Create assignment record
    await this.assignmentHistoryRepository.create(
      orderId,
      'Assign',
      assignedById,
      driverId,
      currentAssignee || undefined,
      vehicleId,
      currentVehicle || undefined,
      reason,
    );

    // Update order with driver and vehicle information
    await this.orderRepository.update(order.id, order.tenantId, {
      assignedDriverId: driverId,
      assignedVehicleId: vehicleId,
    });

    // Update order status to Assigned if currently Pending or Submitted
    if (
      order.status === OrderStatus.Pending ||
      order.status === OrderStatus.Submitted
    ) {
      await this.orderStatusService.updateOrderStatus(
        order,
        OrderStatus.Assigned,
        assignedById,
        'Order assigned to driver',
      );
    }

    // Emit assignment event
    this.eventEmitter.emit('order.assigned', {
      orderId,
      driverId,
      assignedById,
      vehicleId,
    });

    if (driverId && driverId !== order.assignedDriverId) {
      const [newDriver, oldDriver] = await Promise.all([
        this.driverService.getDriverDetails(driverId),
        order.assignedDriverId
          ? this.driverService.getDriverDetails(order.assignedDriverId)
          : Promise.resolve(null),
      ]);

      await this.historyService.create({
        tenantId: order.tenantId,
        entity: HISTORY_ENTITIES.ORDER,
        entityId: order.id,
        property: 'assignedDriverId',
        propertyDataType: 'string',
        oldValue: oldDriver?.name || '',
        newValue: newDriver?.name,
        createdBy: assignedById,
        updatedBy: assignedById,
      });
    }

    return true;
  }

  /**
   * Unassign an order from the current driver/vehicle
   */
  async unassignOrder(
    orderId: string,
    assignedById: string,
    reason?: string,
  ): Promise<boolean> {
    // Get the order
    const order = await this.orderRepository.findById(orderId);
    if (!order) {
      return false;
    }

    // Check if order is locked
    if (order.isLocked) {
      return false;
    }

    // Get the current assignment info for history recording
    const currentAssignee =
      await this.assignmentHistoryRepository.findLatestAssigneeByOrderId(
        orderId,
      );

    const currentVehicle =
      await this.assignmentHistoryRepository.findLatestVehicleByOrderId(
        orderId,
      );

    if (!currentAssignee) {
      // Order is not currently assigned
      return false;
    }

    // Create unassignment record
    await this.assignmentHistoryRepository.create(
      orderId,
      'Unassign',
      assignedById,
      undefined, // new assignee = null (unassigned)
      currentAssignee,
      undefined, // new vehicle = null (unassigned)
      currentVehicle || undefined,
      reason,
    );

    if (order.assignedDriverId) {
      const currentAssigneeDetails = await this.driverService.getDriverDetails(
        order.assignedDriverId,
      );
      await this.historyService.create({
        tenantId: order.tenantId,
        entity: HISTORY_ENTITIES.ORDER,
        entityId: order.id,
        property: 'assignedDriverId',
        propertyDataType: 'string',
        oldValue: currentAssigneeDetails.name,
        newValue: '',
        createdBy: assignedById,
        updatedBy: assignedById,
      });
    }

    console.log({ order });
    // Update order to remove driver and vehicle information
    await this.orderRepository.update(order.id, order.tenantId, {
      //@ts-expect-error quick fix
      assignedDriverId: null,
      //@ts-expect-error quick fix
      assignedVehicleId: null,
    });

    // Update order status back to Pending if currently Assigned
    if (order.status === OrderStatus.Assigned) {
      await this.orderStatusService.updateOrderStatus(
        order,
        OrderStatus.Pending,
        assignedById,
        'Order unassigned from driver',
      );
    }

    // Emit unassignment event
    this.eventEmitter.emit('order.unassigned', {
      orderId,
      previousDriverId: currentAssignee,
      previousVehicleId: currentVehicle,
      assignedById,
    });

    return true;
  }

  /**
   * Reassign an order to a different driver/vehicle
   */
  async reassignOrder(
    orderId: string,
    newDriverId: string,
    assignedById: string,
    newVehicleId?: string,
    reason?: string,
  ): Promise<boolean> {
    // Get the order
    const order = await this.orderRepository.findById(orderId);
    if (!order) {
      return false;
    }

    // Check if order is locked
    if (order.isLocked) {
      return false;
    }

    // Get the current assignment info for history recording
    const currentAssignee =
      await this.assignmentHistoryRepository.findLatestAssigneeByOrderId(
        orderId,
      );
    const currentVehicle =
      await this.assignmentHistoryRepository.findLatestVehicleByOrderId(
        orderId,
      );

    // Create reassignment record
    await this.assignmentHistoryRepository.create(
      orderId,
      'Reassign',
      assignedById,
      newDriverId,
      currentAssignee || undefined,
      newVehicleId,
      currentVehicle || undefined,
      reason,
    );

    // Update order with new driver and vehicle information
    await this.orderRepository.update(order.id, order.tenantId, {
      assignedDriverId: newDriverId,
      assignedVehicleId: newVehicleId,
    });

    // Update order status to Assigned if needed
    if (
      order.status === OrderStatus.Pending ||
      order.status === OrderStatus.Submitted
    ) {
      await this.orderStatusService.updateOrderStatus(
        order,
        OrderStatus.Assigned,
        assignedById,
        'Order reassigned to different driver',
      );
    }

    // Emit reassignment event
    this.eventEmitter.emit('order.reassigned', {
      orderId,
      newDriverId,
      previousDriverId: currentAssignee,
      newVehicleId,
      previousVehicleId: currentVehicle,
      assignedById,
    });

    return true;
  }

  /**
   * Get the assignment history for an order
   */
  async getAssignmentHistory(orderId: string) {
    return this.assignmentHistoryRepository.findByOrderId(orderId);
  }

  /**
   * Get the latest assignment for an order
   */
  async getLatestAssignment(orderId: string) {
    return this.assignmentHistoryRepository.findLatestByOrderId(orderId);
  }

  /**
   * Get the current driver assigned to an order
   */
  async getCurrentAssignee(orderId: string) {
    return this.assignmentHistoryRepository.findLatestAssigneeByOrderId(
      orderId,
    );
  }

  /**
   * Get the current vehicle assigned to an order
   */
  async getCurrentVehicle(orderId: string) {
    return this.assignmentHistoryRepository.findLatestVehicleByOrderId(orderId);
  }
}
