import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { OrderEntity } from '../entities/order.entity';
import { OrderItemEntity } from '../entities/order-item.entity';
import { Order } from '../../domain/order';
import { OrderMapper } from '../mappers/order.mapper';
import { NullableType } from '@utils/types/nullable.type';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { OrderResponseDto } from '../../dto/order-response.dto';
import { OrderStatus } from '../../domain/order.types';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { PackageTemplateEntity } from '@app/business/order/package-templates/infrastructure/entities/package-template.entity';
import { PriceSetModifierEntity } from '../../../../pricing/price-sets/infrastructure/entities/price-set-modifier.entity';
import { ConfigurationType } from '../../../../pricing/price-sets/domain/price-set.types';

@Injectable()
export class OrderRepository {
  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectRepository(OrderItemEntity)
    private readonly orderItemRepository: Repository<OrderItemEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(AddressEntity)
    private readonly addressRepository: Repository<AddressEntity>,
    @InjectRepository(PackageTemplateEntity)
    private readonly packageTemplateRepository: Repository<PackageTemplateEntity>,
    private readonly filterService: SecureFilterService,
    private readonly dataSource: DataSource,
  ) {}

  async create(data: Partial<Order>): Promise<Order> {
    const entityData = OrderMapper.toEntity(data);
    const newEntity = await this.orderRepository.save(entityData);
    return OrderMapper.toDomain(newEntity as OrderEntity);
  }

  async findById(id: string): Promise<NullableType<Order>> {
    const entity = await this.orderRepository.findOne({
      where: { id, isDeleted: false },
      relations: [
        'customer',
        'requestedBy',
        'submittedBy',
        'packageTemplate',
        'collectionAddress',
        'collectionZone',
        'deliveryAddress',
        'deliveryZone',
        'assignedVehicle',
        'vehicleType',
        'priceSet',
        'items',
        'assignedDriver',
        'assignedVehicle',
        'assignedVehicle.vehicleType',
      ],
    });

    if (!entity) return null;

    const requestedByName = await this.getRequestedByName(id);

    const domain = OrderMapper.toDomain(entity);
    (domain as any).customerContactName = requestedByName;

    return domain;

    //  return entity ? OrderMapper.toDomain(entity) : null;
  }

  async getRequestedByName(orderId: string): Promise<string | null> {
    const result = await this.orderRepository
      .createQueryBuilder('o')
      .leftJoin('contacts', 'c', 'c.id = o.requested_by_id')
      .where('o.id = :orderId', { orderId })
      .andWhere('o.deleted_at IS NULL')
      .select('c.name', 'requestedByName')
      .getRawOne();

    return result?.requestedByName || null;
  }

  async findByIdWithRelations(id: string): Promise<NullableType<OrderEntity>> {
    return this.orderRepository.findOne({
      where: { id, isDeleted: false },
      relations: [
        'customer',
        'requestedBy',
        'submittedBy',
        'packageTemplate',
        'collectionAddress',
        'collectionZone',
        'deliveryAddress',
        'deliveryZone',
        'vehicleType',
        'priceSet',
        'items',
        'statusHistory',
        'assignmentHistory',
        'stopHistory',
      ],
    });
  }

  async findByTrackingNumber(
    trackingNumber: string,
  ): Promise<NullableType<Order>> {
    const entity = await this.orderRepository.findOne({
      where: { trackingNumber, isDeleted: false },
      relations: ['assignedVehicle'],
    });

    return entity ? OrderMapper.toDomain(entity) : null;
  }

  async findByTenantId(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.customer', 'customer')
      .leftJoinAndSelect('order.packageTemplate', 'packageTemplate')
      .leftJoinAndSelect('order.collectionAddress', 'collectionAddress')
      .leftJoinAndSelect('order.deliveryAddress', 'deliveryAddress')
      .leftJoinAndSelect('order.priceSet', 'priceSet')
      .leftJoinAndSelect('order.assignedDriver', 'assignedDriver')
      .leftJoinAndSelect('order.collectionZone', 'collectionZone')
      .leftJoinAndSelect('order.deliveryZone', 'deliveryZone')
      .where('order.tenantId = :tenantId', { tenantId })
      .andWhere('order.isDeleted = false');

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = await Promise.all(
      result.data.map((orderEntity: OrderEntity) => {
        const domain = OrderMapper.toDomain(orderEntity);
        const responseDto = new OrderResponseDto();

        // Map basic fields
        responseDto.id = domain.id;
        responseDto.tenantId = domain.tenantId;
        responseDto.trackingNumber = domain.trackingNumber;
        responseDto.referenceNumber = domain.referenceNumber;
        responseDto.status = domain.status;
        responseDto.customerId = domain.customerId;
        responseDto.requestedById = domain.requestedById;
        responseDto.collectionAddressId = domain.collectionAddressId;
        responseDto.collectionContactName = domain.collectionContactName;
        responseDto.collectionInstructions = domain.collectionInstructions;
        responseDto.collectionSignatureRequired =
          domain.collectionSignatureRequired;
        responseDto.scheduledCollectionTime = domain.scheduledCollectionTime;
        responseDto.actualCollectionTime = domain.actualCollectionTime;
        responseDto.deliveryAddressId = domain.deliveryAddressId;
        responseDto.deliveryContactName = domain.deliveryContactName;
        responseDto.deliveryInstructions = domain.deliveryInstructions;
        responseDto.deliverySignatureRequired =
          domain.deliverySignatureRequired;
        responseDto.scheduledDeliveryTime = domain.scheduledDeliveryTime;
        responseDto.actualDeliveryTime = domain.actualDeliveryTime;
        responseDto.totalItems = domain.totalItems;
        responseDto.totalWeight = domain.totalWeight;
        responseDto.totalVolume = domain.totalVolume;
        responseDto.declaredValue = domain.declaredValue;
        responseDto.basePrice = domain.basePrice;
        responseDto.optionsPrice = domain.optionsPrice;
        responseDto.miscAdjustment = domain.miscAdjustment;
        responseDto.customerAdjustment = domain.customerAdjustment;
        responseDto.totalPrice = domain.totalPrice;
        responseDto.billingStatus = domain.billingStatus;
        responseDto.paymentStatus = domain.paymentStatus;
        responseDto.distance = domain.distance;
        responseDto.distanceUnit = domain.distanceUnit;
        responseDto.estimatedDuration = domain.estimatedDuration;
        responseDto.description = domain.description;
        responseDto.codCollected = domain.codCollected;
        responseDto.comments = domain.comments;
        responseDto.assignedDriverId = domain.assignedDriverId;
        responseDto.isLocked = domain.isLocked;
        responseDto.internalNotes = domain.internalNotes;
        responseDto.createdAt = domain.createdAt;
        responseDto.updatedAt = domain.updatedAt;
        // responseDto.cod_amount = domain.cod_amount;

        //note : invoiceNumber is hardcoded for demo purposes because as of now we don't have invoice generation logic
        responseDto.invoiceNumber = 'INV-15452123';

        // Add related entity names if available
        if (orderEntity.customer) {
          const customer = orderEntity.customer;

          responseDto.customerName = customer.contactName;
          responseDto.customerContactName = customer.contactName;
          responseDto.companyName = customer.companyName;
          responseDto.customerEmail = customer.email;

          if (customer.phoneNumber) {
            responseDto.customerPhoneNumber = customer.phoneExtension
              ? `${customer.phoneNumber} ext. ${customer.phoneExtension}`
              : customer.phoneNumber;
          }
        }
        if (orderEntity.assignedDriver) {
          responseDto.assignedDriverName =
            orderEntity.assignedDriver.contactName;
        }

        if (orderEntity.priceSet) {
          responseDto.priceSet = orderEntity.priceSet.name;
          responseDto.serviceLevel = orderEntity.priceSet.internalName;
        }

        if (orderEntity.collectionAddress) {
          responseDto.collectionCompanyName =
            orderEntity.collectionAddress.companyName;
        }
        if (orderEntity.assignedVehicle) {
          responseDto.assignedVehicleDescription = `${orderEntity.assignedVehicle.make} (${orderEntity.assignedVehicle.model})`;
        }

        if (orderEntity.deliveryAddress) {
          responseDto.deliveryCompanyName =
            orderEntity.deliveryAddress.companyName;
        }
        if (orderEntity.collectionAddress) {
          responseDto.collectionAddressSummary = `${orderEntity.collectionAddress.addressLine1},${orderEntity.collectionAddress.addressLine2}, ${orderEntity.collectionAddress.postalCode},${orderEntity.collectionAddress.city}, ${orderEntity.collectionAddress.province}`;
        }

        if (orderEntity.deliveryAddress) {
          responseDto.deliveryAddressSummary = `${orderEntity.deliveryAddress.addressLine1},${orderEntity.deliveryAddress.addressLine2}, ${orderEntity.deliveryAddress.postalCode},${orderEntity.deliveryAddress.city}, ${orderEntity.deliveryAddress.province}`;
        }

        if (orderEntity.collectionZone) {
          responseDto.collectionZoneName = orderEntity?.collectionZone?.name;
        }

        if (orderEntity.deliveryZone) {
          responseDto.deliveryZoneName = orderEntity?.deliveryZone?.name;
        }

        return responseDto;
      }),
    );

    return { ...result, data: mappedData };
  }

  async update(
    id: string,
    tenantId: string,
    data: Partial<Order>,
  ): Promise<NullableType<Order>> {
    const order = await this.orderRepository.findOne({
      where: { id, tenantId, isDeleted: false },
    });

    if (!order) {
      return null;
    }

    const updates = OrderMapper.toEntity(data);
    const updated = await this.orderRepository.save({
      ...order,
      ...updates,
    });

    return OrderMapper.toDomain(updated);
  }

  async updateStatus(
    id: string,
    status: OrderStatus,
    userId: string,
  ): Promise<NullableType<Order>> {
    const order = await this.orderRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!order) {
      return null;
    }

    order.status = status;
    order.updatedBy = userId;

    const updated = await this.orderRepository.save(order);
    return OrderMapper.toDomain(updated);
  }

  async softDelete(
    id: string,
    tenantId: string,
    userId: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findOne({
      where: { id, tenantId, isDeleted: false },
    });

    if (!order) {
      return false;
    }

    order.isDeleted = true;
    order.deletedAt = new Date();
    order.updatedBy = userId;

    await this.orderRepository.save(order);
    return true;
  }

  async restore(
    id: string,
    tenantId: string,
    userId: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findOne({
      where: { id, tenantId, isDeleted: true },
    });

    if (!order) {
      return false;
    }

    order.isDeleted = false;
    order.deletedAt = new Date();
    order.updatedBy = userId;

    await this.orderRepository.save(order);
    return true;
  }

  async hardDelete(id: string, tenantId: string): Promise<boolean> {
    const result = await this.orderRepository.delete({
      id,
      tenantId,
    });

    return result.affected ? result.affected > 0 : false;
  }

  async isLocked(id: string): Promise<boolean> {
    const order = await this.orderRepository.findOne({
      where: { id, isDeleted: false },
      select: ['id', 'isLocked'],
    });

    return order ? order.isLocked : false;
  }

  async lockOrder(
    id: string,
    userId: string,
    reason?: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!order) {
      return false;
    }

    order.isLocked = true;
    order.lockedBy = userId;
    order.lockReason = reason || '';
    order.lockTimestamp = new Date();

    await this.orderRepository.save(order);
    return true;
  }

  async unlockOrder(id: string): Promise<boolean> {
    const order = await this.orderRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!order) {
      return false;
    }

    order.isLocked = false;
    order.lockedBy = '';
    order.lockReason = '';
    order.lockTimestamp = new Date();

    await this.orderRepository.save(order);
    return true;
  }

  async getOrdersWithStatus(
    tenantId: string,
    status: OrderStatus | OrderStatus[],
  ): Promise<Order[]> {
    const statusCondition = Array.isArray(status)
      ? { status: In(status) }
      : { status };

    const orders = await this.orderRepository.find({
      where: {
        tenantId,
        isDeleted: false,
        ...statusCondition,
      },
    });

    return orders.map((order) => OrderMapper.toDomain(order));
  }

  async countOrders(tenantId: string): Promise<number> {
    return this.orderRepository.count({
      where: {
        tenantId,
        isDeleted: false,
      },
    });
  }

  async countOrdersByStatus(
    tenantId: string,
    status: OrderStatus | OrderStatus[],
  ): Promise<number> {
    const statusCondition = Array.isArray(status)
      ? { status: In(status) }
      : { status };

    return this.orderRepository.count({
      where: {
        tenantId,
        isDeleted: false,
        ...statusCondition,
      },
    });
  }

  async getModifierIdsBySetId(
    setId: string,
  ): Promise<
    { memberId: string; isGroup: boolean; configuration: ConfigurationType }[]
  > {
    const result = await this.dataSource
      .getRepository(PriceSetModifierEntity)
      .createQueryBuilder('psm')
      .select([
        'psm.memberId AS "memberId"',
        'psm.isGroup AS "isGroup"',
        'psm.configuration AS "configuration"',
      ])
      .where('psm.setId = :setId', { setId })
      .getRawMany();

    return result;
  }

  async getCustomPricingSummary(
    orderId: string,
    tenantId: string,
  ): Promise<any> {
    const order = await this.orderRepository.findOne({
      where: {
        id: orderId,
        tenantId,
      },
      select: ['customPricingSummary'],
    });

    return order?.customPricingSummary || null;
  }
}
