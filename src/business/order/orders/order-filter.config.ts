import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const OrderFilterConfig = () => {
  const fields = [
    {
      fieldName: 'trackingNumber',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
        ],
        validationMessage: 'Invalid tracking number format',
      },
    },
    {
      fieldName: 'referenceNumber',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'Invalid reference number format',
      },
    },
    {
      fieldName: 'status',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid status value',
      },
    },
    {
      fieldName: 'customerId',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid customer ID format',
      },
    },
    {
      fieldName: 'scheduledCollectionTime',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'Invalid collection time format',
      },
    },
    {
      fieldName: 'scheduledDeliveryTime',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'Invalid delivery time format',
      },
    },
    {
      fieldName: 'billingStatus',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid billing status value',
      },
    },
    {
      fieldName: 'paymentStatus',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid payment status value',
      },
    },
    {
      fieldName: 'collectionAddressId',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid collection address ID format',
      },
    },
    {
      fieldName: 'deliveryAddressId',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid delivery address ID format',
      },
    },
    {
      fieldName: 'totalPrice',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        validationMessage: 'Invalid price value',
      },
    },
    {
      fieldName: 'createdAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        type: 'date' as const,
        validationMessage: 'Invalid date format for createdAt',
      },
    },
    {
      fieldName: 'updatedAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        type: 'date' as const,
        validationMessage: 'Invalid date format for updatedAt',
      },
    },
  ];

  const orderConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    orderConfig.addField(fieldName, options);
  });

  return orderConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields([
      'trackingNumber',
      'referenceNumber',
      'description',
      'comments',
      'internalNotes',
    ])
    .setSortableFields([
      'trackingNumber',
      'referenceNumber',
      'status',
      'scheduledCollectionTime',
      'scheduledDeliveryTime',
      'totalPrice',
      'billingStatus',
      'paymentStatus',
      'createdAt',
      'updatedAt',
    ])
    .build();
};
