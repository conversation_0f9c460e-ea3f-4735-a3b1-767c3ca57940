/**
 * Template variables that can be used in the bill of lading template
 */
export enum TEMPLATE_VARIABLE {
  // tenant Info
  TENANT_NAME = 'TEMPLATE_VARIABLE.TENANT_NAME',
  TENANT_PHONE_NUMBER = 'TEMPLATE_VARIABLE.TENANT_PHONE_NUMBER',
  TENANT_WEBSITE = 'TEMPLATE_VARIABLE.TENANT_WEBSITE',

  // Basic order information
  TRACKING_NUMBER = 'TEMPLATE_VARIABLE.TRACKING_NUMBER',
  SUBMITTED_DATE = 'TEMPLATE_VARIABLE.SUBMITTED_DATE',

  // Sender information
  SENDER_NAME = 'TEMPLATE_VARIABLE.SENDER_NAME',
  SENDER_ADDRESS = 'TEMPLATE_VARIABLE.SENDER_ADDRESS',

  // Receiver information
  RECEIVER_NAME = 'TEMPLATE_VARIABLE.RECEIVER_NAME',
  RECEIVER_ADDRESS = 'TEMPLATE_VARIABLE.RECEIVER_ADDRESS',

  // Service details
  SERVICE_LEVEL = 'TEMPLATE_VARIABLE.SERVICE_LEVEL',
  EXPECTED_COLLECTION_TIME = 'TEMPLATE_VARIABLE.EXPECTED_COLLECTION_TIME',
  EXPECTED_DELIVERY_TIME = 'TEMPLATE_VARIABLE.EXPECTED_DELIVERY_TIME',
  ACTUAL_COLLECTION_TIME = 'TEMPLATE_VARIABLE.ACTUAL_COLLECTION_TIME',
  ACTUAL_DELIVERY_TIME = 'TEMPLATE_VARIABLE.ACTUAL_DELIVERY_TIME',

  // Package information
  DESCRIPTION = 'TEMPLATE_VARIABLE.DESCRIPTION',
  QUANTITY = 'TEMPLATE_VARIABLE.QUANTITY',
  WEIGHT = 'TEMPLATE_VARIABLE.WEIGHT',
  DIMENSIONS = 'TEMPLATE_VARIABLE.DIMENSIONS',
  DECLARED_VALUE = 'TEMPLATE_VARIABLE.DECLARED_VALUE',
  DELIVERY_INSTRUCTIONS = 'TEMPLATE_VARIABLE.DELIVERY_INSTRUCTIONS',

  // Reference information
  PO_NUMBER = 'TEMPLATE_VARIABLE.PO_NUMBER',
  DEPARTMENT = 'TEMPLATE_VARIABLE.DEPARTMENT',
  REF_NUMBER = 'TEMPLATE_VARIABLE.REF_NUMBER',

  // Special variables
  BARCODE = 'TEMPLATE_VARIABLE.BARCODE',
  BARCODE_WITH_TEXT = 'TEMPLATE_VARIABLE.BARCODE_WITH_TEXT',

  //other
  DRIVER_NAME = 'TEMPLATE_VARIABLE.DRIVER',
  VEHICLE_NUMBER = 'TEMPLATE_VARIABLE.VEHICLE_NUMBER',
}
