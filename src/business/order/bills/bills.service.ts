import {
  Injectable,
  InternalServerErrorException,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
  HttpStatus,
} from '@nestjs/common';
import { PuppeteerService } from '../../../utils/puppeteer.service';
import { OrderBills } from './bills.controller';
import { billOfLadingHTMLTemplate } from './templates/billOfLading';
import { shippingLabelHTMLTemplate } from './templates/shippingLabel';
import { wayBillHTMLTemplate } from './templates/wayBill';
import { OrdersService } from '../orders/orders.service';
import { TenantsService } from '../../user/tenants/tenants.service';
import { AppException } from '../../../utils/errors/app.exception';
import { ErrorCode } from '../../../utils/errors/error-codes';
import { TemplateParser } from './utils/template-parser.util';

@Injectable()
export class BillsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(BillsService.name);

  constructor(
    private readonly puppeteerService: PuppeteerService,
    private readonly orderService: OrdersService,
    private readonly tenantsService: TenantsService,
  ) {}

  async onModuleInit() {
    // Initialize browser in PuppeteerService
    await this.puppeteerService.initBrowser();
    this.logger.log('BillsService initialized with TypeScript templates');
  }

  async onModuleDestroy() {
    // PuppeteerService handles browser cleanup
  }

  async getBill(
    orderId: string,
    billName: OrderBills = OrderBills.BILL_OF_LADING,
    tenantId: string,
  ): Promise<Buffer> {
    try {
      const order = await this.orderService.findOne(tenantId, orderId);

      if (!order) {
        throw new AppException(
          `Order with ID ${orderId} not found`,
          ErrorCode.ORDER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      // Retrieve tenant information for template variables
      const tenant = await this.tenantsService.findById(tenantId);

      let html: string;

      // Generate HTML based on the bill type
      switch (billName) {
        case OrderBills.BILL_OF_LADING:
          html = billOfLadingHTMLTemplate;
          break;
        case OrderBills.WAY_BILL:
          html = wayBillHTMLTemplate;
          break;
        case OrderBills.SHIPPING_LABEL:
          html = shippingLabelHTMLTemplate;
          break;
        default: //TODO: throw error
          html = billOfLadingHTMLTemplate;
      }

      let options: any = {};
      if (billName === OrderBills.SHIPPING_LABEL) {
        options = {
          format: '',
          width: '600px',
          height: '8in',
          margin: { top: '0px', right: '0px', bottom: '0px', left: '0px' },
        };
      }
      console.log(' going for parsed');
      html = TemplateParser.parseTemplate(html, order, tenant);
      console.log('parsed');

      // Generate PDF using PuppeteerService
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(
        html,
        options,
      );
      return pdfBuffer;
    } catch (error: any) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw new InternalServerErrorException('Failed to generate bill PDF');
    }
  }
}
