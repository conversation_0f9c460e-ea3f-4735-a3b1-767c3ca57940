import { Module, forwardRef } from '@nestjs/common';
import { BillsController } from './bills.controller';
import { BillsService } from './bills.service';
import { PuppeteerService } from '../../../utils/puppeteer.service';
import { OrdersModule } from '../orders/orders.module';
import { TenantsModule } from '../../user/tenants/tenants.module';

@Module({
  imports: [forwardRef(() => OrdersModule), TenantsModule],
  controllers: [BillsController],
  providers: [BillsService, PuppeteerService],
  exports: [BillsService],
})
export class BillsModule {}
