import JsBarcode from 'jsbarcode';
import { createCanvas } from 'canvas';
import { OrderDetailResponseDto } from '../../orders/dto/order-detail-response.dto';
import { Tenant } from '../../../user/tenants/domain/tenant';
import { formatDateTime } from '../../../pricing/utils/date.utils';
import { DEFAULT_DATETIME_FORMAT_12H } from '../../../pricing/constants/timezone.constants';
import { TEMPLATE_VARIABLE } from '../constants/template-variables.constant';

/**
 * Interface for barcode generation options
 */
export interface BarcodeOptions {
  width?: number;
  height?: number;
  format?: 'CODE128' | 'CODE39' | 'EAN13' | 'EAN8' | 'UPC' | 'ITF14';
  displayValue?: boolean;
  fontSize?: number;
  textAlign?: 'left' | 'center' | 'right';
  textPosition?: 'bottom' | 'top';
  background?: string;
  lineColor?: string;
  margin?: number;
}

/**
 * Template parser utility for processing template variables in bill of lading templates
 */
export class TemplateParser {
  private static readonly DEFAULT_BARCODE_OPTIONS: BarcodeOptions = {
    width: 2,
    height: 70,
    format: 'CODE128',
    displayValue: true,
    fontSize: 12,
    textAlign: 'center',
    textPosition: 'bottom',
    background: '#ffffff',
    lineColor: '#000000',
    margin: 10,
  };

  /**
   * Parse template string and replace template variables with actual data
   * @param template - The template string containing TEMPLATE_VARIABLE placeholders
   * @param data - The data to replace template variables with
   * @param tenant - The tenant data for tenant-specific variables
   * @param fallbackLogoUrl - The full URL for the fallback logo
   * @returns Parsed template string with variables replaced
   */
  public static parseTemplate(
    template: string,
    data: OrderDetailResponseDto,
    tenant?: Tenant,
    fallbackLogoUrl?: string,
  ): string {
    let parsedTemplate = template;

    Object.values(TEMPLATE_VARIABLE).forEach((variable) => {
      // Build regex for exact match of the placeholder
      const regex = new RegExp(`\\b${this.escapeRegExp(variable)}\\b`, 'g');

      // Get replacement value (fallback to empty string if undefined/null)
      const replacement =
        this.getVariableValue(variable, data, tenant, fallbackLogoUrl) ?? '';
      console.log({ variable, replacement });

      // Use a function to prevent special characters ($, \) in the replacement
      parsedTemplate = parsedTemplate.replace(regex, () => String(replacement));
    });

    return parsedTemplate;
  }

  /**
   * Get the value for a specific template variable
   * @param variable - The template variable
   * @param data - The data object
   * @param tenant - The tenant data for tenant-specific variables
   * @param fallbackLogoUrl - The full URL for the fallback logo
   * @returns The value to replace the variable with
   */
  private static getVariableValue(
    variable: TEMPLATE_VARIABLE,
    data: OrderDetailResponseDto,
    tenant?: Tenant,
    fallbackLogoUrl?: string,
  ): string {
    // console.log({ data });

    switch (variable) {
      case TEMPLATE_VARIABLE.TRACKING_NUMBER:
        return data.trackingNumber || '';

      case TEMPLATE_VARIABLE.SUBMITTED_DATE:
        return data.createdAt
          ? formatDateTime(data.createdAt, DEFAULT_DATETIME_FORMAT_12H)
          : 'NA';

      case TEMPLATE_VARIABLE.SENDER_NAME:
        return `${data.collectionCompanyName || ''}, ${data.collectionCompanyName || ''}`;

      case TEMPLATE_VARIABLE.SENDER_ADDRESS:
        return (
          `${data.collectionAddressLine1}, ${data.collectionAddressLine2}, ${data.collectionCity}, ${data.collectionPostalCode}` ||
          ''
        );

      case TEMPLATE_VARIABLE.RECEIVER_NAME:
        return `${data.deliveryCompanyName || ''}, ${data.deliveryCompanyName || ''}`;

      case TEMPLATE_VARIABLE.RECEIVER_ADDRESS:
        return (
          `${data.deliveryAddressLine1}, ${data.deliveryAddressLine2}, ${data.deliveryCity}, ${data.deliveryPostalCode}` ||
          ''
        );

      case TEMPLATE_VARIABLE.SERVICE_LEVEL:
        return data.serviceLevel || '';

      case TEMPLATE_VARIABLE.EXPECTED_COLLECTION_TIME:
        return data.actualCollectionTime
          ? formatDateTime(
              data.actualCollectionTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.EXPECTED_DELIVERY_TIME:
        return data.scheduledDeliveryTime
          ? formatDateTime(
              data.scheduledDeliveryTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.ACTUAL_COLLECTION_TIME:
        return data.actualCollectionTime
          ? formatDateTime(
              data.actualCollectionTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.ACTUAL_DELIVERY_TIME:
        return data.actualDeliveryTime
          ? formatDateTime(data.actualDeliveryTime, DEFAULT_DATETIME_FORMAT_12H)
          : 'NA';

      case TEMPLATE_VARIABLE.DESCRIPTION:
        return data.description || 'NA';

      case TEMPLATE_VARIABLE.DELIVERY_INSTRUCTIONS:
        return data.internalNotes || 'NA';

      case TEMPLATE_VARIABLE.QUANTITY:
        return String(data.totalItems) || '';

      case TEMPLATE_VARIABLE.WEIGHT:
        return String(data.totalWeight) || '';

      case TEMPLATE_VARIABLE.DIMENSIONS:
        return 'NA'; //TODO:NEED TO DISCUSS

      case TEMPLATE_VARIABLE.DECLARED_VALUE:
        return String(data.declaredValue) || '';

      case TEMPLATE_VARIABLE.PO_NUMBER:
        return data.trackingNumber || '';

      case TEMPLATE_VARIABLE.DEPARTMENT:
        return 'Tech'; //TODO: ADD DEPARTMENT

      case TEMPLATE_VARIABLE.REF_NUMBER:
        return data.referenceNumber || 'NA';

      case TEMPLATE_VARIABLE.DRIVER_NAME:
        return data.assignedDriverName || 'NA';

      case TEMPLATE_VARIABLE.BARCODE:
        return this.generateBarcodeDataUrl(data.trackingNumber || '', {
          displayValue: false,
        });

      case TEMPLATE_VARIABLE.BARCODE_WITH_TEXT:
        return this.generateBarcodeDataUrl(data.trackingNumber || '', {
          displayValue: true,
        });

      case TEMPLATE_VARIABLE.TENANT_LOGO:
        // Return tenant logo if available and not empty, otherwise fallback to Lumigo logo
        console.log('TENANT_LOGO processing:', {
          tenant,
          logoUrl: tenant?.logoUrl,
          fallbackLogoUrl,
        });
        const tenantLogo = tenant?.logoUrl?.trim();
        if (tenantLogo && tenantLogo !== '') {
          console.log('Using tenant logo:', tenantLogo);
          return tenantLogo;
        }
        const fallbackUrl = fallbackLogoUrl || '/assets/lumigologo.svg';
        console.log('Using fallback logo:', fallbackUrl);
        return fallbackUrl;

      default:
        return '';
    }
  }

  /**
   * Generate a barcode as a data URL
   * @param value - The value to encode in the barcode
   * @param options - Barcode generation options
   * @returns Data URL string for the barcode image
   */
  private static generateBarcodeDataUrl(
    value: string,
    options: Partial<BarcodeOptions> = {},
  ): string {
    if (!value) {
      return '';
    }

    const barcodeOptions = { ...this.DEFAULT_BARCODE_OPTIONS, ...options };

    try {
      // Create a canvas for barcode generation
      const canvas = createCanvas(300, barcodeOptions.height || 70);

      // Generate barcode using JsBarcode
      JsBarcode(canvas, value, {
        format: barcodeOptions.format,
        width: barcodeOptions.width,
        height: barcodeOptions.height,
        displayValue: barcodeOptions.displayValue,
        fontSize: 16,
        textAlign: barcodeOptions.textAlign,
        textPosition: barcodeOptions.textPosition,
        background: barcodeOptions.background,
        lineColor: barcodeOptions.lineColor,
        margin: barcodeOptions.margin,
      });
      console.log({ aaa: canvas.toDataURL('image/png') });

      // Convert canvas to data URL
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('Error generating barcode:', error);
      // Fallback to SVG barcode if canvas fails
      return this.createFallbackBarcodeSvg(value, barcodeOptions);
    }
  }

  /**
   * Create a fallback SVG barcode when canvas generation fails
   */
  private static createFallbackBarcodeSvg(
    value: string,
    options: BarcodeOptions,
  ): string {
    // This is a placeholder implementation
    // In production, you would use a proper barcode generation library
    const width = 200;
    const height = options.height || 70;

    // Create a simple SVG barcode pattern
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${width}" height="${height}" fill="${options.background}"/>
        ${this.generateBarcodePattern(value, width, height - 20)}
        ${options.displayValue ? `<text x="${width / 2}" y="${height - 5}" text-anchor="middle" font-family="monospace" font-size="12" fill="${options.lineColor}">${value}</text>` : ''}
      </svg>
    `;

    // Convert SVG to data URL
    const base64 = Buffer.from(svg).toString('base64');
    return `data:image/svg+xml;base64,${base64}`;
  }

  /**
   * Generate a simple barcode pattern based on the value
   */
  private static generateBarcodePattern(
    value: string,
    width: number,
    height: number,
  ): string {
    const bars: string[] = [];
    const barWidth = width / (value.length * 8); // Approximate bar width

    for (let i = 0; i < value.length; i++) {
      const charCode = value.charCodeAt(i);
      const pattern = charCode.toString(2).padStart(8, '0'); // Convert to binary

      for (let j = 0; j < pattern.length; j++) {
        const x = (i * 8 + j) * barWidth;
        if (pattern[j] === '1') {
          bars.push(
            `<rect x="${x}" y="0" width="${barWidth}" height="${height}" fill="#000000"/>`,
          );
        }
      }
    }

    return bars.join('');
  }

  /**
   * Escape special regex characters in a string
   */
  private static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Get all available template variables
   */
  public static getAvailableVariables(): TEMPLATE_VARIABLE[] {
    return Object.values(TEMPLATE_VARIABLE);
  }

  /**
   * Validate if a template contains valid template variables
   */
  public static validateTemplate(template: string): {
    isValid: boolean;
    invalidVariables: string[];
  } {
    const templateVariablePattern = /TEMPLATE_VARIABLE\.[A-Z_]+/g;
    const foundVariables = template.match(templateVariablePattern) || [];
    const validVariables = Object.values(TEMPLATE_VARIABLE);

    const invalidVariables = foundVariables.filter(
      (variable) => !validVariables.includes(variable as TEMPLATE_VARIABLE),
    );

    return {
      isValid: invalidVariables.length === 0,
      invalidVariables,
    };
  }
}
