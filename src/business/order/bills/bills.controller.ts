import {
  <PERSON>,
  Get,
  UseGuards,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiCookieAuth,
  ApiProduces,
} from '@nestjs/swagger';
import { BillsService } from './bills.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { Response } from 'express';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';

@ApiTags('Business - Order - Bills')
@ApiBearerAuth()
@ApiCookieAuth()
@Controller({
  path: '/orders',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class BillsController {
  constructor(private readonly billsService: BillsService) {}

  // // Simple endpoint for testing
  // @Get()
  // @ApiOperation({ summary: 'Generate and return default bill PDF' })
  // @ApiOkResponse({ description: 'PDF binary stream' })
  // @Header('Content-Type', 'application/pdf')
  // @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  // @ApiProduces('application/pdf')
  // async getDefaultBill(@Res() res: Response): Promise<any> {
  //   const sampleData: BillOfLadingData = {
  //     trackingNumber: 'TRK-2025-001234',
  //     submittedDate: new Date().toLocaleDateString('en-US', {
  //       year: 'numeric',
  //       month: '2-digit',
  //       day: '2-digit',
  //       hour: '2-digit',
  //       minute: '2-digit',
  //       hour12: true,
  //     }),
  //   };

  //   const pdfBuffer = await this.billsService.getBillOfLading(sampleData);
  //   res.set({
  //     'Content-Type': 'application/pdf',
  //     'Content-Disposition': 'inline; filename="bill-of-lading.pdf"',
  //     'Content-Length': pdfBuffer.length,
  //   });
  //   res.send(pdfBuffer);
  // }

  @Get(':orderId/bills/:billType')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @ApiProduces('application/pdf')
  async getBill(
    @CurrentUser() contactData: JwtPayload,
    @Param('orderId') orderId: string,
    @Param('billType') billType: OrderBills,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ): Promise<any> {
    const tenantId = contactData.ctx.tenantId;

    if (!tenantId) {
      throw new Error('Tenant id not found');
    }
    console.log({ contactData });

    const pdfBuffer = await this.billsService.getBill(
      orderId,
      billType,
      tenantId,
    );
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${billType}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }
}

export enum OrderBills {
  WAY_BILL = 'wayBill',
  BILL_OF_LADING = 'billOfLading',
  SHIPPING_LABEL = 'shippingLabel',
}
