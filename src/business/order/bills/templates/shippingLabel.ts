export const shippingLabelHTMLTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Shipping Label</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { font-family: sans-serif; }
    .border-primary-900 { border-color: #1a202c; }
    .text-primary-900 { color: #1a202c; }
  </style>
</head>
<body class="bg-white">
  <div class="w-[600px] p-4 bg-white text-primary-900 mx-auto">
    <!-- Header -->
    <div class="flex justify-between items-start mb-2">
      <div>
        <img src="https://via.placeholder.com/150x40?text=Lumigo+Logo" alt="Lumigo" class="h-8 mb-2" />
        <p class="text-xs">
          Lumigo Solution 2015 Inc (Lumigo Transport)<br />
          Phone: ************<br />
          www.lumigotransport.ca
        </p>
      </div>
      <div class="text-sm font-semibold">Shipping label</div>
    </div>

    <!-- Sender / Receiver -->
    <div class="grid grid-cols-2 border border-primary-900 mb-2 text-sm">
      <div class="flex gap-2 p-2 pb-12 border-r border-r-black">
        <h2 class="font-semibold mb-1">Sender:</h2>
        <p>
          UrbanPrint Works,<br />
          7520 Chemin de la Côte-de-Liesse<br />
          Saint-Laurent<br />
          H4T 1E7
        </p>
      </div>
      <div class="flex gap-2 p-2 pb-4">
        <h2 class="font-semibold mb-1">Receiver:</h2>
        <p>
          MAJESTIX, NOOR,<br />
          340 Rue Alme-Vincent<br />
          ************<br />
          Vaudreuil-Dorion<br />
          J7V 5V5
        </p>
      </div>
    </div>

    <!-- Tracking Section -->
    <div class="flex justify-between gap-2 mb-2 border border-primary-900 p-2">
      <div class="flex flex-col ga-2 items-start text-sm">
        <span class="font-semibold">Sameday</span>
        <span>Tracking : #147795</span>
      </div>
      <img src="data:image/svg+xml;base64,PHN2ZyB...your-barcode-data..." alt="Barcode" class="w-[200px] h-[70px] text-lg" />
    </div>

    <!-- Package / Receiver Info -->
    <div class="grid grid-cols-2 text-sm border border-primary-900">
      <div class="p-2 pb-[150px] border-r border-r-black flex flex-col gap-2">
        <h2 class="font-semibold mb-1">Package details:</h2>
        <p>
          <strong>Description:</strong><br />1 Package + 1 Box = Total: 2
        </p>
        <p>
          <strong>Dimensions:</strong><br />0L x 0W x 0H
        </p>
        <p>
          <strong>Weight:</strong><br />1
        </p>
      </div>
      <div class="p-2">
        <h2 class="font-semibold mb-1 pb-[150px]">Receiver information:</h2>
      </div>
    </div>
  </div>
</body>
</html>
`;
