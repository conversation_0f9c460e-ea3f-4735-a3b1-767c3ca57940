import { TEMPLATE_VARIABLE } from '../constants/template-variables.constant';

export const billOfLadingHTMLTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bill of Lading</title>

  <!-- Tailwind CSS CDN (for backend rendering in Puppeteer) -->
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { font-family: sans-serif; }
    .border-primary-900 { border-color: #1a202c; } /* Tailwind's gray-900 */
    .bg-[#E3E5F6] { background-color: #f9fafb; }
    .text-primary-900 { color: #1a202c; }
  </style>
</head>
<body class="bg-white p-2 text-sm text-primary-900 font-sans max-w-4xl mx-auto ">

  <!-- Header -->
  <div class="flex justify-between items-start mb-4">
    <div>
      <img src="${TEMPLATE_VARIABLE.TENANT_LOGO}" alt="Company Logo" class="h-8 mb-2" />
      <p class="text-xs">
        Lumigo Solution 2015 Inc (Lumigo Transport) <br />
        Phone: ************ Website: www.lumigotransport.ca
      </p>
    </div>
    <div class="text-right">
      <h2 class="text-2xl font-semibold">Bill of Lading</h2>
      <p class="text-xs">Submitted on: ${TEMPLATE_VARIABLE.SUBMITTED_DATE}</p>
    </div>
  </div>

  <!-- Tracking Number -->
  <div class="bg-[#E3E5F6] border border-primary-900 py-2 px-4 mb-4 text-center font-bold text-base">
    Tracking number: #${TEMPLATE_VARIABLE.TRACKING_NUMBER}
  </div>

  <!-- Sender / Receiver -->
  <div class="grid grid-cols-2 border border-primary-900 mb-4">
    <div class="border-r border-primary-900">
      <div class="px-2 pt-2 pb-4 flex gap-2">
        <p class="font-semibold">Sender:</p>
        <p>
          ${TEMPLATE_VARIABLE.SENDER_NAME}<br />
          ${TEMPLATE_VARIABLE.SENDER_ADDRESS}
        </p>
      </div>
    </div>
    <div class="p-2 flex gap-2">
      <p class="font-semibold">Receiver:</p>
      <p>
        ${TEMPLATE_VARIABLE.RECEIVER_NAME}<br />
        ${TEMPLATE_VARIABLE.RECEIVER_ADDRESS}
      </p>
    </div>
  </div>

  <!-- Barcode Section -->
  <div class="flex items-center gap-4 border border-primary-900 p-3 mb-4 flex-wrap">
    <div class="w-[32%]">
      <div class="flex flex-col justify-start items-center w-fit">
        <img src="${TEMPLATE_VARIABLE.BARCODE_WITH_TEXT}" alt="barcode" class="w-[200px] h-[70px]" />
      </div>
    </div>
    <div class="text-xs text-right flex gap-2 flex-col flex-1 ">
      <p class="flex flex-col items-start text-sm">
        <span class="font-semibold">Service Level:</span> ${TEMPLATE_VARIABLE.SERVICE_LEVEL}
      </p>
      <div class="flex justify-between">
        <p class="text-sm border block">
          <span class="font-semibold">Collect at:</span> ${TEMPLATE_VARIABLE.EXPECTED_COLLECTION_TIME}
        </p>
        <p class="text-sm block">
          <span class="font-semibold">Delivery at:</span> ${TEMPLATE_VARIABLE.EXPECTED_DELIVERY_TIME}
        </p>
      </div>
    </div>
  </div>

  <!-- Shipment Info Table -->
  <div class="border border-primary-900 divide-y divide-black mb-4">
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Description:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.DESCRIPTION}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">PO Number:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.PO_NUMBER}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Quantity:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.QUANTITY}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Weight:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.WEIGHT}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Dimensions:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.DIMENSIONS}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Department:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.DEPARTMENT}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Ref. Number:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.REF_NUMBER}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Declared Number:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.DECLARED_VALUE}</div>
    </div>
    <div class="flex">
      <div class="w-[20%] p-2 font-bold border-r border-primary-900">Shipper:</div>
      <div class="w-[80%] p-2">${TEMPLATE_VARIABLE.SENDER_NAME}, ${TEMPLATE_VARIABLE.SENDER_ADDRESS}</div>
    </div>
  </div>

  <!-- Signature Section -->
  <div class="grid grid-cols-2 gap-6 text-xs">
    <div>
      <div class="bg-[#E3E5F6] px-2 py-2 w-fit font-bold border border-primary-900 text-sm">Collection</div>
      <div class="flex flex-col gap-6 p-2 pt-4 space-y-2">
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Driver:</p>
          <div class="border-b border-primary-900 w-full h-4">${TEMPLATE_VARIABLE.DRIVER_NAME}</div>
        </div>
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Vehicle:</p>
          <div class="border-b border-primary-900 w-full h-4"></div>
        </div>
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Date & time:</p>
          <div class="border-b border-primary-900 w-full h-4">${TEMPLATE_VARIABLE.ACTUAL_COLLECTION_TIME}</div>
        </div>
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Received from:</p>
          <div class="border-b border-primary-900 w-full h-4"></div>
        </div>
        <div class="flex gap-1 mt-6">
          <p class="font-bold min-w-max text-sm">Signature:</p>
          <div class="border-b border-primary-900 w-full h-4"></div>
        </div>
      </div>
    </div>
    <div>
      <div class="bg-[#E3E5F6] px-2 py-2 w-fit font-bold border border-primary-900 text-sm">Delivery</div>
      <div class="flex flex-col gap-6 p-2 pt-4 space-y-2">
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Driver:</p>
          <div class="border-b border-primary-900 w-full h-4">${TEMPLATE_VARIABLE.DRIVER_NAME}</div>
        </div>
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Vehicle:</p>
          <div class="border-b border-primary-900 w-full h-4"></div>
        </div>
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Date & time:</p>
          <div class="border-b border-primary-900 w-full h-4">${TEMPLATE_VARIABLE.ACTUAL_DELIVERY_TIME}</div>
        </div>
        <div class="flex gap-1 text-sm">
          <p class="font-bold min-w-max">Received from:</p>
          <div class="border-b border-primary-900 w-full h-4"></div>
        </div>
        <div class="flex gap-1 mt-6">
          <p class="font-bold min-w-max text-sm">Signature:</p>
          <div class="border-b border-primary-900 w-full h-4"></div>
        </div>
      </div>
    </div>
  </div>

</body>
</html>
`;
