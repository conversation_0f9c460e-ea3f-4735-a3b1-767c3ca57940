import { Module } from '@nestjs/common';
import { MobileOrdersController } from './orders.controller';
import { MobileOrdersService } from './services/mobile-orders.service';
import { MobileAuthModule } from '../auth/auth.module';
import { OrdersModule } from '@app/business/order/orders/orders.module';
import { UsersModule } from '@app/business/user/users/users.module';
import { VehiclesModule } from '@app/business/vehicle/vehicles/vehicles.module';
import { FileStorageModule } from '@app/core/file-storage/file-storage.module';
import { FileUploadModule } from '@app/core/file-upload/file-upload.module';
import { PriceSetsModule } from '@app/business/pricing/price-sets/price-sets.module';
import { TimeClockSessionModule } from '@app/business/vehicle/time-clock-session/time-clock-session.module';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    MobileAuthModule,
    OrdersModule,
    UsersModule,
    VehiclesModule,
    FileStorageModule,
    FileUploadModule,
    PriceSetsModule,
    TimeClockSessionModule,
    TypeOrmModule.forFeature([]),
  ],
  controllers: [MobileOrdersController],
  providers: [MobileOrdersService],
  exports: [MobileOrdersService],
})
export class MobileOrdersModule {}
