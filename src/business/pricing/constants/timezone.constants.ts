/**
 * Timezone constants for the pricing module
 *
 * These constants will be used for standardizing date/time handling across the application.
 * In the future, these values will be fetched from tenant settings.
 */

/**
 * Default timezone to use for all date/time operations
 * This ensures consistent date handling regardless of server location
 */
export const DEFAULT_TIMEZONE = 'Asia/Calcutta'; // Will be replaced with tenant settings in the future

/**
 * Date format to use for standardized date strings
 */
export const DEFAULT_DATE_FORMAT = 'YYYY-MM-DD';

/**
 * Time format to use for standardized time strings
 */
export const DEFAULT_TIME_FORMAT = 'HH:mm:ss';

/**
 * DateTime format to use for standardized datetime strings
 */
export const DEFAULT_DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

/**
 * DateTime format to use for standardized datetime strings in 12-hour format
 */
export const DEFAULT_DATETIME_FORMAT_12H = 'YYYY-MM-DD h:mm A';
