import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { PriceModifiersService } from './price-modifiers.service';
import { CreatePriceModifierDto } from './dto/create-price-modifier.dto';
import {
  GetAllPriceModifierDto,
  GetPriceModifierDto,
} from './dto/get-price-modifier.dto';
import { CreatePriceModifierGroupDto } from './dto/create-price-modifier-group.dto';
import {
  GetAllPriceModifierGroupDto,
  GetPriceModifierGroupDto,
} from './dto/get-price-modifier-group.dto';
import { PriceModifierDomain } from './domain/price-modifier';
import { PriceModifierGroupDomain } from './domain/price-modifier-group';
import { CalculationResultDto } from '@app/business/pricing/price-modifiers/dto/calculation-result.dto';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { TenantValidationService } from '../../user/tenants/tenant-validation.service';
import { CalculatePriceDto } from './dto/calculate-price.dto';
import { GetCombinedDto } from './dto/get-combined.dto';

@ApiTags('Business - Pricing - Price Modifiers')
@ApiBearerAuth()
@Controller({
  path: 'price-modifiers',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class PriceModifiersController {
  constructor(
    private readonly priceModifiersService: PriceModifiersService,
    private readonly secureFilterService: SecureFilterService,
    private readonly tenantValidationService: TenantValidationService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Get('combined')
  @ApiOperation({
    summary: 'Get all price modifiers and price modifiers groups',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifiers and groups retrieved successfully',
    type: GetCombinedDto,
  })
  async getAllPriceModifiersAndGroups(
    @Req() request: RequestWithUser,
  ): Promise<GetCombinedDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    try {
      const data =
        await this.priceModifiersService.getAllPriceModifiersAndGroups(
          tenantId,
        );

      return { data };
    } catch (error) {
      throw error;
    }
  }

  // Individual Price Modifiers
  @Post()
  @ApiOperation({ summary: 'Create new price modifier' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({
    description: 'Price modifier created successfully',
    type: GetPriceModifierDto,
  })
  async createPriceModifier(
    @Req() request: RequestWithUser,
    @Body() createPriceModifierDto: CreatePriceModifierDto,
  ): Promise<GetPriceModifierDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const modifierDomain = this.mapper.map(
      createPriceModifierDto,
      CreatePriceModifierDto,
      PriceModifierDomain,
    );
    modifierDomain.tenantId = tenantId;

    const priceModifier =
      await this.priceModifiersService.createPriceModifier(modifierDomain);

    const responseDomain = this.mapper.map(
      priceModifier,
      PriceModifierDomain,
      GetPriceModifierDto,
    );
    return responseDomain;
  }

  @Get()
  @ApiOperation({
    summary: 'Get all price modifiers with pagination and advance filters',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifiers retrieved successfully',
    type: GetAllPriceModifierDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  async getPriceModifiers(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllPriceModifierDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );
    const result = await this.priceModifiersService.findAllPriceModifier(
      tenantId,
      combinedFilter,
    );

    const mappedData = this.mapper.mapArray(
      result.data,
      PriceModifierDomain,
      GetPriceModifierDto,
    );

    const response: GetAllPriceModifierDto = {
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPreviousPage: result.hasPreviousPage,
      data: mappedData,
    };
    return response;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get price modifier by ID' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifier retrieved successfully',
    type: GetPriceModifierDto,
  })
  async getPriceModifierById(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<PriceModifierDomain> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const priceModifier = await this.priceModifiersService.getPriceModifierById(
      id,
      tenantId,
    );
    return priceModifier;
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update price modifier' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifier updated successfully',
    type: GetPriceModifierDto,
  })
  async updatePriceModifier(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() updatePriceModifierDto: CreatePriceModifierDto,
  ): Promise<GetPriceModifierDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const modifierDomain = this.mapper.map(
      updatePriceModifierDto,
      CreatePriceModifierDto,
      PriceModifierDomain,
    );
    modifierDomain.id = id;
    modifierDomain.tenantId = tenantId;

    const priceModifier =
      await this.priceModifiersService.updatePriceModifier(modifierDomain);

    const responseDomain = this.mapper.map(
      priceModifier,
      PriceModifierDomain,
      GetPriceModifierDto,
    );
    return responseDomain;
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete price modifier' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'Price modifier deleted successfully' })
  async deletePriceModifier(@Param('id') id: string): Promise<void> {
    await this.priceModifiersService.deletePriceModifier(id);
    return;
  }

  // Price Modifier Groups
  @Post('groups')
  @ApiOperation({ summary: 'Create new price modifier group' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({
    description: 'Price modifier group created successfully',
    type: GetPriceModifierGroupDto,
  })
  async createPriceModifierGroup(
    @Req() request: RequestWithUser,
    @Body() createGroupDto: CreatePriceModifierGroupDto,
  ): Promise<GetPriceModifierGroupDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const groupDomain = this.mapper.map(
      createGroupDto,
      CreatePriceModifierGroupDto,
      PriceModifierGroupDomain,
    );
    groupDomain.tenantId = tenantId;

    const group =
      await this.priceModifiersService.createPriceModifierGroup(groupDomain);

    const responseDomain = this.mapper.map(
      group,
      PriceModifierGroupDomain,
      GetPriceModifierGroupDto,
    );
    return responseDomain;
  }

  @Get('/get/groups')
  @ApiOperation({
    summary:
      'Get all price modifier groups with pagination and advance filters',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifier groups retrieved successfully',
    type: GetAllPriceModifierGroupDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  async getPriceModifierGroups(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllPriceModifierGroupDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );
    const result = await this.priceModifiersService.findAllGroups(
      tenantId,
      combinedFilter,
    );

    const mappedData = this.mapper.mapArray(
      result.data,
      PriceModifierGroupDomain,
      GetPriceModifierGroupDto,
    );

    const response: GetAllPriceModifierGroupDto = {
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPreviousPage: result.hasPreviousPage,
      data: mappedData,
    };
    return response;
  }

  @Get('groups/:id')
  @ApiOperation({ summary: 'Get price modifier group by ID' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifier group retrieved successfully',
    type: GetPriceModifierGroupDto,
  })
  async getPriceModifierGroupById(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<GetPriceModifierGroupDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const group = await this.priceModifiersService.getPriceModifierGroupById(
      id,
      tenantId,
    );

    const mappedGroup = this.mapper.map(
      group,
      PriceModifierGroupDomain,
      GetPriceModifierGroupDto,
    );
    return mappedGroup;
  }

  @Put('groups/:id')
  @ApiOperation({ summary: 'Update price modifier group' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Price modifier group updated successfully',
    type: GetPriceModifierGroupDto,
  })
  async updatePriceModifierGroup(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() updateGroupDto: CreatePriceModifierGroupDto,
  ): Promise<GetPriceModifierGroupDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    const groupDomain = this.mapper.map(
      updateGroupDto,
      CreatePriceModifierGroupDto,
      PriceModifierGroupDomain,
    );
    groupDomain.id = id;
    groupDomain.tenantId = tenantId;

    const group =
      await this.priceModifiersService.updatePriceModifierGroup(groupDomain);

    const responseDomain = this.mapper.map(
      group,
      PriceModifierGroupDomain,
      GetPriceModifierGroupDto,
    );
    return responseDomain;
  }

  @Delete('groups/:id')
  @ApiOperation({ summary: 'Delete price modifier group' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({
    description: 'Price modifier group deleted successfully',
  })
  async deletePriceModifierGroup(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.priceModifiersService.deletePriceModifierGroup(id, tenantId);
    return;
  }

  // @Post('calculate')
  // @ApiOperation({ summary: 'Calculate price with modifiers' })
  // @HttpCode(HttpStatus.OK)
  // @ApiOkResponse({
  //   description: 'Price calculation result',
  //   type: CalculationResultDto,
  // })
  // @ApiBadRequestResponse({
  //   description: 'Invalid modifiers or calculation error',
  // })
  // @ApiForbiddenResponse({
  //   description: 'Insufficient tenant access permissions',
  // })
  // async calculatePrice(
  //   @Req() request: RequestWithUser,
  //   @Body() calculatePriceDto: CalculatePriceDto,
  // ): Promise<CalculationResultDto> {
  //   const { id: tenantId } =
  //     await this.tenantValidationService.validateTenantAccess(request);

  //   const result = await this.priceModifiersService.calculatePrice(
  //     tenantId,
  //     calculatePriceDto.order,
  //     calculatePriceDto.memberIds,
  //   );
  //   return result;
  // }
}
