export enum ECalculationType {
  FlatAmount = 'FlatAmount',
  FlatPercentage = 'FlatPercentage',
  FlatOverageAmount = 'FlatOverageAmount',
  FlatOveragePercentage = 'FlatOveragePercentage',
  TieredFixedOverageAmount = 'TieredFixedOverageAmount',
  TieredFixedOveragePercentage = 'TieredFixedOveragePercentage',
  IncrementalOverageAmount = 'IncrementalOverageAmount',
  IncrementalOveragePercentage = 'IncrementalOveragePercentage',
  TieredIncrementalOverageAmount = 'TieredIncrementalOverageAmount',
  TieredIncrementalOveragePercentage = 'TieredIncrementalOveragePercentage',
}

export enum ECalculationField {
  PriceModifier = 'PriceModifier',
  BasePrice = 'BasePrice',
  DeclaredPrice = 'DeclaredPrice',
  CubicDimensions = 'CubicDimensions',
  Distance = 'Distance',
  Height = 'Height',
  Width = 'Width',
  Length = 'Length',
  Quantity = 'Quantity',
  CollectionWaitTime = 'CollectionWaitTime',
  DeliveryWaitTime = 'DeliveryWaitTime',
  Weight = 'Weight',
  CustomAmount = 'CustomAmount',
}

export enum EModifierGroupBehavior {
  UseSum = 'UseSum',
  UseHighest = 'UseHighest',
  UseLowest = 'UseLowest',
}

export interface TieredRange {
  fromValue: number;
  fromOperator: ERangeFromOperator;
  toValue: number | null;
  toOperator: ERangeToOperator;
  value: number;
}

/**
 * Enumeration of range from operators with descriptions.
 */
export enum ERangeFromOperator {
  GreaterThan = 'GreaterThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
}

/**
 * Enumeration of range to operators with descriptions.
 */
export enum ERangeToOperator {
  LessThan = 'LessThan',
  LessThanOrEqual = 'LessThanOrEqual',
}
