import { AutoMap } from '@automapper/classes';
import { ECalculationType, TieredRange } from './price-modifier.types';

export class PriceModifierDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  calculationType: ECalculationType;

  @AutoMap()
  fieldName: string;

  @AutoMap()
  applicableRangeMin?: number | null;

  @AutoMap()
  applicableRangeMax?: number | null;

  @AutoMap()
  calculationBase?: number;

  @AutoMap()
  increment?: number;

  @AutoMap()
  amount?: number;

  @AutoMap()
  tieredRanges?: TieredRange[];

  @AutoMap()
  tieredDefaultValue: number;

  @AutoMap()
  isActive: boolean;

  @AutoMap()
  metadata?: Record<string, any>;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}
