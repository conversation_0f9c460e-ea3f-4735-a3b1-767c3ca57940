import { Injectable } from '@nestjs/common';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { DataSource } from 'typeorm';
import { PriceModifierRepository } from './infrastructure/repositories/price-modifier.repository';
import { PriceModifierGroupRepository } from './infrastructure/repositories/price-modifier-group.repository';
import { PriceModifierDomain } from './domain/price-modifier';
import { PriceModifierGroupDomain } from './domain/price-modifier-group';
import {
  PriceModifierAlreadyExistsException,
  PriceModifierGroupAlreadyExistsException,
  PriceModifierGroupNotFoundException,
  PriceModifierGroupOperationNotAllowedException,
  PriceModifierGroupTenantMismatchException,
  PriceModifierNotFoundException,
  PriceModifierOperationNotAllowedException,
  PriceModifierTenantMismatchException,
} from '@utils/errors/exceptions/price-modifier-exceptions';
import {
  ECalculationType,
  EModifierGroupBehavior,
  ERangeFromOperator,
  ERangeToOperator,
  TieredRange,
} from './domain/price-modifier.types';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { PriceModifierGroupMemberEntity } from '@app/business/pricing/price-modifiers/infrastructure/entities/price-modifier-group-member.entity';
import {
  CalculationField,
  CalculationType,
  GroupBehavior,
  IPriceModifier,
  RangeOperator,
} from '@core/pricing/domain/price-modifier.interface';
import { OrderDetailsDto } from '@app/business/pricing/price-modifiers/dto/calculate-price.dto';
import { ICalculationResult } from '@core/pricing/domain/calculation-result.type';
import { PriceCalculatorService } from '@core/pricing/price-calculator.service';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { ConfigurationType } from '../price-sets/domain/price-set.types';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PriceModifiersService {
  constructor(
    private readonly priceModifierRepository: PriceModifierRepository,
    private readonly priceModifierGroupRepository: PriceModifierGroupRepository,
    private readonly dataSource: DataSource,
    private readonly priceCalculatorService: PriceCalculatorService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async getAllPriceModifiersAndGroups(tenantId: string) {
    const modifiers =
      await this.priceModifierRepository.findByTenantId(tenantId);

    const groups =
      await this.priceModifierGroupRepository.findByTenantId(tenantId);

    const modifiedModifiers = modifiers.map((item) => ({
      ...item,
      isGroup: false,
    }));

    const modifiedGroups = groups.map((item) => ({
      ...item,
      isGroup: true,
    }));

    const allModifiersAndGroups = [...modifiedModifiers, ...modifiedGroups];

    // Sort by createdAt
    allModifiersAndGroups.sort((a, b) => {
      if (a.createdAt > b.createdAt) {
        return -1;
      }
      if (a.createdAt < b.createdAt) {
        return 1;
      }
      return 0;
    });

    const response = allModifiersAndGroups.map((item) => ({
      id: item.id,
      name: item.name,
      isGroup: item.isGroup,
    }));

    return response;
  }

  // Individual Price Modifiers
  async createPriceModifier(
    modifierDomain: PriceModifierDomain,
  ): Promise<PriceModifierDomain> {
    const existingModifier = await this.priceModifierRepository.findOne({
      name: modifierDomain.name,
      tenantId: modifierDomain.tenantId,
    });
    if (existingModifier) {
      throw new PriceModifierAlreadyExistsException(
        modifierDomain.name,
        modifierDomain.tenantId,
      );
    }

    // ✅ Handle applicable range logic as per condition
    const minProvided = modifierDomain.applicableRangeMin != null;
    const maxProvided = modifierDomain.applicableRangeMax != null;

    if (!minProvided && !maxProvided) {
      modifierDomain.applicableRangeMin = null;
      modifierDomain.applicableRangeMax = null;
    } else {
      modifierDomain.applicableRangeMin = minProvided
        ? modifierDomain.applicableRangeMin
        : 0;
      modifierDomain.applicableRangeMax = maxProvided
        ? modifierDomain.applicableRangeMax
        : 9999999;
    }

    // ✅ Normalize tiered ranges if provided
    if (modifierDomain.tieredRanges?.length) {
      modifierDomain.tieredRanges = modifierDomain.tieredRanges.map((range) => {
        const fromProvided = range.fromValue != null;
        const toProvided = range.toValue != null;

        return {
          ...range,
          fromValue: fromProvided ? range.fromValue : 0,
          toValue: toProvided ? range.toValue : 9999999,
        };
      });
    }

    this.validateModifierFields(modifierDomain);
    if (Array.isArray(modifierDomain.tieredRanges)) {
      this.validateTieredRanges(modifierDomain.tieredRanges);
    }
    const responseDomain =
      await this.priceModifierRepository.create(modifierDomain);
    return responseDomain;
  }

  async findAllPriceModifier(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PriceModifierDomain>> {
    const priceModifier = await this.priceModifierRepository.find(
      tenantId,
      filter,
    );
    return priceModifier;
  }

  async getPriceModifierById(
    id: string,
    tenantId: string,
  ): Promise<PriceModifierDomain> {
    const modifier = await this.priceModifierRepository.findOne({ id });
    if (!modifier) {
      throw new PriceModifierNotFoundException(id);
    }

    if (modifier.tenantId !== tenantId) {
      throw new PriceModifierTenantMismatchException(id, tenantId);
    }

    return modifier;
  }

  async updatePriceModifier(
    modifierDomain: PriceModifierDomain,
  ): Promise<PriceModifierDomain> {
    const existingModifier = await this.getPriceModifierById(
      modifierDomain.id,
      modifierDomain.tenantId,
    );
    if (modifierDomain.name !== existingModifier.name) {
      const duplicateModifier = await this.priceModifierRepository.findOne({
        name: modifierDomain.name,
        tenantId: modifierDomain.tenantId,
      });
      if (duplicateModifier) {
        throw new PriceModifierAlreadyExistsException(
          modifierDomain.name,
          modifierDomain.tenantId,
        );
      }
    }

    const minProvided = modifierDomain.applicableRangeMin != null;
    const maxProvided = modifierDomain.applicableRangeMax != null;

    if (!minProvided && !maxProvided) {
      modifierDomain.applicableRangeMin = null;
      modifierDomain.applicableRangeMax = null;
    } else {
      modifierDomain.applicableRangeMin = minProvided
        ? modifierDomain.applicableRangeMin
        : 0;
      modifierDomain.applicableRangeMax = maxProvided
        ? modifierDomain.applicableRangeMax
        : 9999999;
    }

    // ✅ Normalize tiered ranges
    if (modifierDomain.tieredRanges?.length) {
      modifierDomain.tieredRanges = modifierDomain.tieredRanges.map((range) => {
        const fromProvided = range.fromValue != null;
        const toProvided = range.toValue != null;

        return {
          ...range,
          fromValue: fromProvided ? range.fromValue : 0,
          toValue: toProvided ? range.toValue : 9999999,
        };
      });
    }
    this.validateModifierFields(modifierDomain);
    if (Array.isArray(modifierDomain.tieredRanges)) {
      this.validateTieredRanges(modifierDomain.tieredRanges);
    }
    const priceModifier = this.priceModifierRepository.update(modifierDomain);
    return priceModifier;
  }

  async deletePriceModifier(id: string): Promise<void> {
    const isInGroup =
      await this.priceModifierGroupRepository.isModifierInAnyGroup(id);

    if (isInGroup) {
      throw new PriceModifierOperationNotAllowedException(
        'delete',
        'This price modifier is used in one or more groups. Remove it from all groups first.',
      );
    }

    await this.priceModifierRepository.delete(id);
    return;
  }

  async createPriceModifierGroup(
    groupDomain: PriceModifierGroupDomain,
  ): Promise<PriceModifierGroupDomain> {
    const existingGroup = await this.priceModifierGroupRepository.findOne({
      name: groupDomain.name,
      tenantId: groupDomain.tenantId,
    });

    if (existingGroup) {
      throw new PriceModifierGroupAlreadyExistsException(
        groupDomain.name,
        groupDomain.tenantId,
      );
    }

    const tempGroupId = uuidv4(); // TEMP ID for validation only

    if (groupDomain.members.length > 0) {
      const memberIds = groupDomain.members
        .filter((m) => !m.isGroup)
        .map((m) => m.id);

      const groupIds = groupDomain.members
        .filter((m) => m.isGroup)
        .map((m) => m.id);

      const modifiers = await this.priceModifierRepository.findByIds(memberIds);

      if (modifiers.length !== memberIds.length) {
        const foundIds = modifiers.map((m) => m.id);
        const missingIds = memberIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifiers not found',
        );
      }

      const invalidModifiers = modifiers.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifiers.length > 0) {
        throw new PriceModifierTenantMismatchException(
          invalidModifiers[0].id,
          groupDomain.tenantId,
        );
      }

      const groups =
        await this.priceModifierGroupRepository.findByIds(groupIds);
      if (groups.length !== groupIds.length) {
        const foundIds = groups.map((m) => m.id);
        const missingIds = groupIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierGroupNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifier groups not found',
        );
      }

      const invalidGroups = groups.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidGroups.length > 0) {
        throw new PriceModifierGroupTenantMismatchException(
          invalidGroups[0].id,
          groupDomain.tenantId,
        );
      }

      // 🔁 Circular reference check
      await this.priceModifierGroupRepository.detectCircularReference(
        tempGroupId,
        groupIds,
      );
    }

    const result = await this.priceModifierGroupRepository.create(groupDomain);
    return result;
  }

  async findAllGroups(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PriceModifierGroupDomain>> {
    const priceModifierGroup = await this.priceModifierGroupRepository.find(
      tenantId,
      filter,
    );
    return priceModifierGroup;
  }

  async getPriceModifierGroupById(
    id: string,
    tenantId: string,
  ): Promise<PriceModifierGroupDomain> {
    const group = await this.priceModifierGroupRepository.findOne({
      id,
    });

    if (!group) {
      throw new PriceModifierGroupNotFoundException(id);
    }

    if (group.tenantId !== tenantId) {
      throw new PriceModifierTenantMismatchException(id, tenantId);
    }

    return group;
  }

  async updatePriceModifierGroup(
    groupDomain: PriceModifierGroupDomain,
  ): Promise<PriceModifierGroupDomain> {
    const existingGroup = await this.getPriceModifierGroupById(
      groupDomain.id,
      groupDomain.tenantId,
    );

    if (groupDomain.name !== existingGroup.name) {
      const duplicateGroup = await this.priceModifierGroupRepository.findOne({
        name: groupDomain.name,
        tenantId: groupDomain.tenantId,
      });

      if (duplicateGroup) {
        throw new PriceModifierGroupAlreadyExistsException(
          groupDomain.name,
          groupDomain.tenantId,
        );
      }
    }

    if (groupDomain.members.length > 0) {
      const memberIds = groupDomain.members
        .filter((m) => !m.isGroup)
        .map((m) => m.id);
      const groupIds = groupDomain.members
        .filter((m) => m.isGroup)
        .map((m) => m.id);

      const modifiers = await this.priceModifierRepository.findByIds(memberIds);

      if (modifiers.length !== memberIds.length) {
        const foundIds = modifiers.map((m) => m.id);
        const missingIds = memberIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifiers not found',
        );
      }

      const invalidModifiers = modifiers.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifiers.length > 0) {
        throw new PriceModifierTenantMismatchException(
          invalidModifiers[0].id,
          groupDomain.tenantId,
        );
      }

      const groups =
        await this.priceModifierGroupRepository.findByIds(groupIds);

      if (groups.length !== groupIds.length) {
        const foundIds = groups.map((m) => m.id);
        const missingIds = groupIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierGroupNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifier groups not found',
        );
      }

      const invalidModifierGroup = groups.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifierGroup.length > 0) {
        throw new PriceModifierGroupTenantMismatchException(
          invalidModifierGroup[0].id,
          groupDomain.tenantId,
        );
      }
      await this.priceModifierGroupRepository.detectCircularReference(
        groupDomain.id,
        groupIds,
      );
    }

    const result = this.priceModifierGroupRepository.update(groupDomain);
    return result;
  }

  async deletePriceModifierGroup(id: string, tenantId: string): Promise<void> {
    await this.getPriceModifierGroupById(id, tenantId);

    const isInGroup =
      await this.priceModifierGroupRepository.isModifierInAnyGroup(id);

    if (isInGroup) {
      throw new PriceModifierGroupOperationNotAllowedException(
        'delete',
        'This price modifier group is used in one or more groups. Remove it from all groups first.',
      );
    }
    await this.priceModifierGroupRepository.delete(id);
    return;
  }

  // Helpers
  private validateModifierFields(domain: PriceModifierDomain): void {
    const { calculationType } = domain;

    // Validate required fields based on calculation type
    if (
      [
        ECalculationType.FlatOverageAmount,
        ECalculationType.FlatOveragePercentage,
        ECalculationType.IncrementalOverageAmount,
        ECalculationType.IncrementalOveragePercentage,
      ].includes(calculationType as ECalculationType) &&
      domain.calculationBase === undefined
    ) {
      throw new PriceModifierOperationNotAllowedException(
        'create',
        `calculationBase is required for ${calculationType} calculation type`,
      );
    }

    if (
      [
        ECalculationType.IncrementalOverageAmount,
        ECalculationType.IncrementalOveragePercentage,
      ].includes(calculationType as ECalculationType) &&
      domain.increment === undefined
    ) {
      throw new PriceModifierOperationNotAllowedException(
        'create',
        `increment is required for ${calculationType} calculation type`,
      );
    }

    if (
      [
        ECalculationType.TieredFixedOverageAmount,
        ECalculationType.TieredFixedOveragePercentage,
        ECalculationType.TieredIncrementalOverageAmount,
        ECalculationType.TieredIncrementalOveragePercentage,
      ].includes(calculationType as ECalculationType) &&
      (!domain.tieredRanges || domain.tieredRanges.length === 0)
    ) {
      throw new PriceModifierOperationNotAllowedException(
        'create',
        `tieredRanges are required for ${calculationType} calculation type`,
      );
    }
  }
  private validateTieredRanges(tieredRanges: TieredRange[]): void {
    if (tieredRanges.length < 2) {
      return;
    }

    for (const range of tieredRanges) {
      if (range.toValue !== null && range.fromValue > range.toValue) {
        throw new PriceModifierOperationNotAllowedException(
          'create',
          `Invalid tiered range. fromValue (${range.fromValue}) cannot be greater than toValue (${range.toValue}).`,
        );
      }
    }

    const sortedRanges = [...tieredRanges].sort((a, b) => {
      if (a.fromValue !== b.fromValue) {
        return a.fromValue - b.fromValue;
      }
      if (a.toValue === null) return 1;
      if (b.toValue === null) return -1;
      return a.toValue - b.toValue;
    });
    const seen = new Set<string>();
    const uniqueRanges: typeof sortedRanges = [];

    for (const range of sortedRanges) {
      const key = JSON.stringify({
        fromValue: range.fromValue,
        toValue: range.toValue,
        fromOperator: range.fromOperator,
        toOperator: range.toOperator,
        value: range.value,
      });
      if (!seen.has(key)) {
        seen.add(key);
        uniqueRanges.push(range);
      }
    }
    for (let i = 1; i < uniqueRanges.length; i++) {
      const prev = uniqueRanges[i - 1];
      const curr = uniqueRanges[i];

      if (prev.toValue === null) {
        throw new PriceModifierOperationNotAllowedException(
          'create',
          `Tiered ranges overlap: a range from ${prev.fromValue} to infinity overlaps with the next range.`,
        );
      }

      if (curr.fromValue <= (prev.toValue ?? null)) {
        throw new PriceModifierOperationNotAllowedException(
          'create',
          `Tiered ranges overlap: [${prev.fromValue}-${prev.toValue}] and [${curr.fromValue}-${curr.toValue}].`,
        );
      }
    }
  }

  async calculatePrice(
    tenantId: string,
    orderDetails: OrderDetailsDto,
    members: {
      memberId: string;
      isGroup: boolean;
      configuration: ConfigurationType;
    }[],
  ): Promise<ICalculationResult> {
    const modifierIds = members
      .filter((m) => !m.isGroup)
      .map((m) => m.memberId);

    const groupIds = members.filter((m) => m.isGroup).map((m) => m.memberId);

    const configMap = new Map(
      members.map((m) => [m.memberId, m.configuration]),
    );

    // Step 1: Fetch non-group modifiers
    const modifiers = await this.priceModifierRepository.findByIds(modifierIds);

    const invalidModifiers = modifiers.filter((m) => m.tenantId !== tenantId);
    if (invalidModifiers.length > 0) {
      throw new PriceModifierTenantMismatchException(
        invalidModifiers[0].id,
        tenantId,
      );
    }

    // Step 2: Fetch groups and their member mappings
    const groups =
      groupIds.length > 0
        ? await this.priceModifierGroupRepository.findByIds(groupIds)
        : [];

    let groupMembers: PriceModifierGroupMemberEntity[] = [];
    if (groupIds.length > 0) {
      groupMembers = await this.dataSource
        .getRepository(PriceModifierGroupMemberEntity)
        .createQueryBuilder('member')
        .where('member.groupId IN (:...groupIds)', { groupIds })
        .getMany();
    }

    const groupMemberModifierIds = groupMembers.map((m) => m.memberId);

    const groupMemberModifiers = await this.priceModifierRepository.findByIds(
      groupMemberModifierIds,
    );

    const invalidGroupModifiers = groupMemberModifiers.filter(
      (m) => m.tenantId !== tenantId,
    );
    if (invalidGroupModifiers.length > 0) {
      throw new PriceModifierTenantMismatchException(
        invalidGroupModifiers[0].id,
        tenantId,
      );
    }

    // Step 3: Convert to interface
    const priceModifiers = modifiers.map((modifier) => {
      const config = configMap.get(modifier.id);
      return {
        ...this.mapToPriceModifierInterface(modifier),
        configuration: config,
      };
    });

    const groupModifiers = groups.map((group) => {
      const groupModifierIds = groupMembers
        .filter((m) => m.groupId === group.id)
        .map((m) => m.memberId);
      const groupConfig = configMap.get(group.id);
      const childModifiers = groupMemberModifiers
        .filter((m) => groupModifierIds.includes(m.id))
        .map((m) => ({
          ...this.mapToPriceModifierInterface(m),
          configuration: groupConfig,
        }));

      const childModifierResults = childModifiers.map((child) => {
        const result = this.priceCalculatorService.processModifier(
          orderDetails,
          child,
        );
        return {
          ...result,
          configuration: child.configuration,
        };
      });

      return {
        id: group.id,
        name: group.name,
        calculationType: 'Group' as any,
        calculationField: 'BasePrice' as any,
        value: 0,
        isGroupModifier: true,
        isEnabled: true,
        modifiers: childModifiers,
        children: childModifierResults,
        groupBehavior: this.mapGroupBehavior(group.behavior),
      };
    });

    const allModifiers = [...priceModifiers, ...groupModifiers];

    return this.priceCalculatorService.calculatePriceForOrders(
      orderDetails,
      allModifiers,
    );
  }

  private mapToPriceModifierInterface(
    modifier: PriceModifierDomain,
  ): IPriceModifier {
    // Create the applicable range if min/max values exist
    const applicableRange: any =
      modifier.applicableRangeMin || modifier.applicableRangeMax
        ? {
            from:
              modifier.applicableRangeMin !== undefined
                ? {
                    value: modifier.applicableRangeMin,
                    operator: RangeOperator.GREATER_THAN_OR_EQUAL,
                  }
                : undefined,
            to:
              modifier.applicableRangeMax !== undefined
                ? {
                    value: modifier.applicableRangeMax,
                    operator: RangeOperator.LESS_THAN_OR_EQUAL,
                  }
                : undefined,
          }
        : undefined;

    // Transform tiered ranges to calculation engine format
    const tieredRanges: any =
      modifier.tieredRanges && modifier.tieredRanges.length > 0
        ? modifier.tieredRanges.map((tier) => ({
            from: {
              value: tier.fromValue,
              operator: this.mapRangeOperator(tier.fromOperator),
            },
            to: {
              value: tier.toValue,
              operator: this.mapRangeOperator(tier.toOperator),
            },
            value: tier.value,
          }))
        : undefined;

    return {
      id: modifier.id,
      name: modifier.name,
      calculationType: modifier.calculationType as unknown as CalculationType,
      calculationField: modifier.fieldName as CalculationField,
      value: modifier.amount || 0,
      increment: modifier.increment,
      calculationStartAfter: modifier.calculationBase,
      applicableRange,
      tieredRanges,
      isGroupModifier: false,
      isEnabled: modifier.isActive,
    };
  }

  private mapRangeOperator(
    operator: ERangeFromOperator | ERangeToOperator,
  ): RangeOperator {
    switch (operator) {
      case ERangeFromOperator.GreaterThan:
        return RangeOperator.GREATER_THAN;
      case ERangeFromOperator.GreaterThanOrEqual:
        return RangeOperator.GREATER_THAN_OR_EQUAL;
      case ERangeToOperator.LessThan:
        return RangeOperator.LESS_THAN;
      case ERangeToOperator.LessThanOrEqual:
        return RangeOperator.LESS_THAN_OR_EQUAL;
      default:
        throw new Error(`Unknown range operator: ${operator}`);
    }
  }

  private mapGroupBehavior(behavior: EModifierGroupBehavior): GroupBehavior {
    switch (behavior) {
      case EModifierGroupBehavior.UseSum:
        return GroupBehavior.USE_SUM;
      case EModifierGroupBehavior.UseHighest:
        return GroupBehavior.USE_HIGHEST;
      case EModifierGroupBehavior.UseLowest:
        return GroupBehavior.USE_LOWEST;
      default:
        return GroupBehavior.USE_SUM;
    }
  }
}
