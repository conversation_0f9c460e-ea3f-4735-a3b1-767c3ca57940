import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, In, Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { PriceModifierEntity } from '../entities/price-modifier.entity';
import { PriceModifierDomain } from '../../domain/price-modifier';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../../../utils/query-creator/interfaces';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceModifierFilterConfig } from '../../price-modifier-filter.config';

@Injectable()
export class PriceModifierRepository {
  constructor(
    @InjectRepository(PriceModifierEntity)
    private readonly priceModifierRepository: Repository<PriceModifierEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(PriceModifierFilterConfig());
  }

  async create(data: PriceModifierDomain): Promise<PriceModifierDomain> {
    const requestEntity = this.mapper.map(
      data,
      PriceModifierDomain,
      PriceModifierEntity,
    );

    const priceModifierEntity = await this.priceModifierRepository.save(
      this.priceModifierRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      priceModifierEntity,
      PriceModifierEntity,
      PriceModifierDomain,
    );
    return {
      ...responseDomain,
      applicableRangeMin:
        responseDomain.applicableRangeMin === 0
          ? null
          : responseDomain.applicableRangeMin,
      applicableRangeMax:
        Number(responseDomain.applicableRangeMax) === 9999999
          ? null
          : responseDomain.applicableRangeMax,
      tieredRanges: responseDomain.tieredRanges?.map((r) => ({
        ...r,
        fromValue: r.fromValue === 0 ? null : r.fromValue,
        toValue: Number(r.toValue) === 9999999 ? null : r.toValue,
      })) as any,
    };
  }

  async find(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PriceModifierDomain>> {
    const queryBuilder = this.priceModifierRepository
      .createQueryBuilder('modifier')
      .where('modifier.tenantId =:tenantId', { tenantId });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);
    const mappedData = this.mapper
      .mapArray(result.data, PriceModifierEntity, PriceModifierDomain)
      .map((item) => ({
        ...item,
        applicableRangeMin:
          item.applicableRangeMin === 0 ? null : item.applicableRangeMin,
        applicableRangeMax:
          Number(item.applicableRangeMax) === 9999999
            ? null
            : item.applicableRangeMax,
        tieredRanges: item.tieredRanges?.map((r) => ({
          ...r,
          fromValue: r.fromValue === 0 ? null : r.fromValue,
          toValue: Number(r.toValue) === 9999999 ? null : r.toValue,
        })) as any,
      }));
    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<PriceModifierDomain>,
  ): Promise<NullableType<PriceModifierDomain>> {
    const requestEntity: Partial<PriceModifierEntity> = this.mapper.map(
      fields,
      PriceModifierDomain,
      PriceModifierEntity,
    );

    const priceModifierEntity = await this.priceModifierRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<PriceModifierEntity>),
      },
    });

    if (priceModifierEntity) {
      const responseDomain = this.mapper.map(
        priceModifierEntity,
        PriceModifierEntity,
        PriceModifierDomain,
      );

      if (responseDomain.applicableRangeMin === 0) {
        responseDomain.applicableRangeMin = null;
      }
      if (Number(responseDomain.applicableRangeMax) === 9999999) {
        responseDomain.applicableRangeMax = null;
      }

      if (responseDomain.tieredRanges) {
        responseDomain.tieredRanges = responseDomain.tieredRanges.map((r) => ({
          ...r,
          fromValue: r.fromValue === 0 ? null : r.fromValue,
          toValue: Number(r.toValue) === 9999999 ? null : r.toValue,
        })) as any;
      }

      return responseDomain;
    }
    return null;
  }

  async update(payload: PriceModifierDomain): Promise<PriceModifierDomain> {
    const requestEntity = this.mapper.map(
      payload,
      PriceModifierDomain,
      PriceModifierEntity,
    );
    const priceModifierEntity =
      await this.priceModifierRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      priceModifierEntity,
      PriceModifierEntity,
      PriceModifierDomain,
    );
    return {
      ...responseDomain,
      applicableRangeMin:
        responseDomain.applicableRangeMin === 0
          ? null
          : responseDomain.applicableRangeMin,
      applicableRangeMax:
        Number(responseDomain.applicableRangeMax) === 9999999
          ? null
          : responseDomain.applicableRangeMax,
      tieredRanges: responseDomain.tieredRanges?.map((r) => ({
        ...r,
        fromValue: r.fromValue === 0 ? null : r.fromValue,
        toValue: Number(r.toValue) === 9999999 ? null : r.toValue,
      })) as any,
    };
  }

  async delete(id: string): Promise<void> {
    await this.priceModifierRepository.delete(id);
    return;
  }

  async findByIds(ids: string[]): Promise<PriceModifierDomain[]> {
    const entities = await this.priceModifierRepository.findBy({
      id: In(ids),
    });

    const responseDomain = this.mapper
      .mapArray(entities, PriceModifierEntity, PriceModifierDomain)
      .map((item) => ({
        ...item,
        applicableRangeMin:
          item.applicableRangeMin === 0 ? null : item.applicableRangeMin,
        applicableRangeMax:
          Number(item.applicableRangeMax) === 9999999
            ? null
            : item.applicableRangeMax,
      }));
    return responseDomain;
  }

  async findByTenantId(tenantId: string) {
    const entities = await this.priceModifierRepository.find({
      where: { tenantId },
      select: ['id', 'name', 'createdAt'],
    });

    const responseDomain = this.mapper
      .mapArray(entities, PriceModifierEntity, PriceModifierDomain)
      .map((item) => ({
        ...item,
        applicableRangeMin:
          item.applicableRangeMin === 0 ? null : item.applicableRangeMin,
        applicableRangeMax:
          Number(item.applicableRangeMax) === 9999999
            ? null
            : item.applicableRangeMax,
      }));
    return responseDomain;
  }

  async findModifierById(
    modifierId: string,
  ): Promise<PriceModifierEntity | null> {
    return await this.priceModifierRepository.findOne({
      where: { id: modifierId },
    });
  }
}
