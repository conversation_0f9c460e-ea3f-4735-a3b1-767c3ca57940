import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PriceSetsService } from './price-sets.service';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CreatePriceSetDto } from './dto/create-price-set.dto';
import { PriceSetDomain } from './domain/price-set';
import { GetAllPriceSetDto, GetPriceSetDto } from './dto/get-price-set.dto';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { GetScheduleDto } from './dto/get-schedule.dto';
import {
  GetPriceSetsByDateResponseDto,
  PriceSetParamsDto,
} from './dto/get-price-set-by-date.dto';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { PriceSetOperationNotAllowedException } from '@utils/errors/exceptions/price-set-exceptions';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import {
  AssignModifiersDto,
  EditConfigurationDto,
} from './dto/assign-modifiers.dto';
import { GetPriceSetModifiersResponseDto } from './dto/get-price-modifiers.dto';
import { AssignCustomersDto } from './dto/assign-customers.dto';
import { GetPriceSetCustomersResponseDto } from './dto/get-price-customers.dto';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { Request } from 'express';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { TenantValidationService } from '../../user/tenants/tenant-validation.service';
import {
  GetAllPriceSetMinimalDto,
  GetPriceSetMinimalDto,
} from './dto/get-price-set-minimal.dto';
import { CreateZoneTableDto } from '../../zone/zone-tables/dto/create-zone-table.dto';
import { GetPriceSetZoneResponseDto } from './dto/get-price-zone.dto';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import {
  GetAvailableServicesRequestDto,
  GetAvailableServicesResponseDto,
} from '../../customer-portal/pricing/dto/get-available-services.dto';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import { PricingService } from '../pricing/pricing.service';
import { OrdersService } from '../../order/orders/orders.service';

@ApiTags('Business - Pricing - Price Sets')
@Controller({
  path: 'priceSets',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class PriceSetsController {
  constructor(
    private readonly priceSetsService: PriceSetsService,
    private readonly secureFilterService: SecureFilterService,
    private readonly tenantValidationService: TenantValidationService,
    private readonly pricingService: PricingService,
    private readonly orderService: OrdersService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Price Set' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createPriceSetDto: CreatePriceSetDto,
  ): Promise<PriceSetDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new PriceSetOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const priceSetDomain = this.mapper.map(
        createPriceSetDto,
        CreatePriceSetDto,
        PriceSetDomain,
      );
      priceSetDomain.tenantId = tenantId;
      const priceSet = await this.priceSetsService.create(priceSetDomain);
      return priceSet;
    } catch (error) {
      throw error;
    }
  }

  @Post(':priceSetId/duplicate')
  @ApiOperation({ summary: 'Duplicate Price Set by Price Set Id' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Duplicated' })
  async duplicateAddress(
    @Param('priceSetId') priceSetId: string,
  ): Promise<PriceSetDomain> {
    try {
      const priceSet =
        await this.priceSetsService.duplicatePriceSet(priceSetId);
      return priceSet;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all Price Sets with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllPriceSetDto })
  async getPriceSetList(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllPriceSetDto> {
    try {
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );

      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new PriceSetOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getPriceSetList',
          'Insufficient tenant access permissions',
        );
      }

      const result = await this.priceSetsService.getPriceSetList(
        combinedFilter,
        tenantId,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        PriceSetDomain,
        GetPriceSetDto,
      );

      const response: GetAllPriceSetDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('all/minimal')
  @ApiOperation({
    summary:
      'Get all price sets (id, name and internal name only, no pagination)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllPriceSetMinimalDto })
  async getAllPriceSetsMinimal(
    @Req() request: Request,
  ): Promise<GetAllPriceSetMinimalDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);
      const priceSets = await this.priceSetsService.getAllPriceSets(tenantId);
      const data = this.mapper.mapArray(
        priceSets,
        PriceSetDomain,
        GetPriceSetMinimalDto,
      );
      return { data };
    } catch (error) {
      throw error;
    }
  }

  @Get('pickupDate')
  @ApiOperation({ summary: 'Find available price sets by pickup date' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetPriceSetsByDateResponseDto })
  async getPriceSetsByPickupDate(
    @Req() request: RequestWithUser,
    @Query() queryParams: PriceSetParamsDto,
  ): Promise<GetPriceSetsByDateResponseDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);
      const pickupDate = new Date(queryParams.pickupDate);
      const data = await this.priceSetsService.getPriceSetsByPickupDate(
        pickupDate,
        tenantId,
      );
      return { data };
    } catch (error) {
      throw error;
    }
  }

  @Get(':priceSetId')
  @ApiOperation({ summary: 'Find Price Set by Price Set Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetPriceSetDto })
  async getPriceSetDetails(
    @Param('priceSetId') priceSetId: string,
  ): Promise<GetPriceSetDto> {
    try {
      const responseDomain =
        await this.priceSetsService.getPriceSetDetails(priceSetId);
      const response = this.mapper.map(
        responseDomain,
        PriceSetDomain,
        GetPriceSetDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':priceSetId')
  @ApiOperation({ summary: 'Update Price Set by Price Set Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updatePriceSetDetails(
    @Param('priceSetId') priceSetId: string,
    @Body() updatePriceSetDto: CreatePriceSetDto,
  ): Promise<void> {
    try {
      const priceSet = this.mapper.map(
        updatePriceSetDto,
        CreatePriceSetDto,
        PriceSetDomain,
      );
      priceSet.id = priceSetId;
      await this.priceSetsService.updatePriceSetDetails(priceSet);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':priceSetId')
  @ApiOperation({ summary: 'Soft-delete Price Set by Price Set Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deletePriceSet(@Param('priceSetId') priceSetId: string): Promise<void> {
    try {
      await this.priceSetsService.deletePriceSet(priceSetId);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Post(':priceSetId/schedule')
  @ApiOperation({ summary: 'Create new Price Set Schedule' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async editSchedule(
    @Param('priceSetId') priceSetId: string,
    @Body() createScheduleDto: CreateScheduleDto,
  ): Promise<void> {
    try {
      await this.priceSetsService.editSchedule(priceSetId, createScheduleDto);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Get(':priceSetId/schedule')
  @ApiOperation({ summary: 'Find Price Set Schedule by Price Set Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetScheduleDto })
  async getScheduleDetails(
    @Param('priceSetId') priceSetId: string,
  ): Promise<GetScheduleDto> {
    try {
      const responseDomain =
        await this.priceSetsService.getScheduleDetails(priceSetId);

      return responseDomain;
    } catch (error) {
      throw error;
    }
  }

  @Put(':priceSetId/modifiers')
  @ApiOperation({ summary: 'Assign Modifiers to Price Set' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async assignPriceModifiers(
    @Param('priceSetId') priceSetId: string,
    @Body() assignModifiersDto: AssignModifiersDto,
  ): Promise<void> {
    try {
      await this.priceSetsService.assignPriceModifiers(
        priceSetId,
        assignModifiersDto,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Get(':priceSetId/modifiers')
  @ApiOperation({ summary: 'Get all Price Modifiers linked to Price Set' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetPriceSetModifiersResponseDto })
  async getPriceSetModifiers(
    @Param('priceSetId') priceSetId: string,
  ): Promise<GetPriceSetModifiersResponseDto> {
    try {
      const data = await this.priceSetsService.getPriceSetModifiers(priceSetId);
      return { data };
    } catch (error) {
      throw error;
    }
  }

  @Put('modifiers/:setModifierId')
  @ApiOperation({
    summary: 'Edit Price Modifier configuration assigned to Price Set',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async editConfiguration(
    @Param('setModifierId') setModifierId: string,
    @Body() editConfigurationDto: EditConfigurationDto,
  ): Promise<void> {
    try {
      await this.priceSetsService.editConfiguration(
        setModifierId,
        editConfigurationDto.configuration,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Put(':priceSetId/customers')
  @ApiOperation({ summary: 'Assign Customers to Price Set' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async assignCustomers(
    @Param('priceSetId') priceSetId: string,
    @Body() assignCustomersDto: AssignCustomersDto,
  ): Promise<void> {
    try {
      await this.priceSetsService.assignCustomers(
        priceSetId,
        assignCustomersDto.customerIds,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Get(':priceSetId/customers')
  @ApiOperation({ summary: 'Get all Customers linked to Price Set' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetPriceSetCustomersResponseDto })
  async getPriceSetCustomers(
    @Param('priceSetId') priceSetId: string,
  ): Promise<GetPriceSetCustomersResponseDto> {
    try {
      const data = await this.priceSetsService.getPriceSetCustomers(priceSetId);
      return { data };
    } catch (error) {
      throw error;
    }
  }

  @Put(':priceSetId/zone')
  @ApiOperation({ summary: 'Assign prices for different zones for Price Set' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async basePriceByZone(
    @Req() request: Request,
    @Param('priceSetId') priceSetId: string,
    @Body() createZoneTableDto: CreateZoneTableDto,
  ): Promise<void> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);
      await this.priceSetsService.basePriceByZone(
        tenantId,
        priceSetId,
        createZoneTableDto,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Get(':priceSetId/zone')
  @ApiOperation({ summary: 'Get prices for different zones for Price Set' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetPriceSetZoneResponseDto })
  async getBasePriceByZone(
    @Param('priceSetId') priceSetId: string,
  ): Promise<GetPriceSetZoneResponseDto> {
    try {
      const data = await this.priceSetsService.getBasePriceByZone(priceSetId);
      return data;
    } catch (error) {
      throw error;
    }
  }

  @Get(':orderId/all-available-services')
  @ApiOperation({
    summary: 'Get available services (with pricing) based on an existing order',
  })
  @ApiOkResponse({ description: 'OK', type: GetAvailableServicesResponseDto })
  @HttpCode(HttpStatus.OK)
  async getAvailableServicesFromOrder(
    @CurrentUser() userData: JwtPayload,
    @Param('orderId') orderId: string,
  ): Promise<GetAvailableServicesResponseDto> {
    try {
      const tenantId = userData.ctx.tenantId;
      if (!tenantId) {
        throw new Error('User does not have a tenant ID');
      }

      const order: any = await this.orderService.findOne(tenantId, orderId);
      if (!order) {
        throw new Error(`Order not found with ID: ${orderId}`);
      }

      const requestDto: GetAvailableServicesRequestDto = {
        pickupDate: order.scheduledCollectionTime,
        includePricing: true,
        order: {
          collectionAddressId: order.collectionAddressId,
          deliveryAddressId: order.deliveryAddressId,
          packageTemplateId: order.packageTemplateId,
          // Add any other required fields like weight, size, etc.
        },
      };

      const data = await this.pricingService.getAvailableServicesForAdmin(
        requestDto,
        tenantId,
      );

      return { data };
    } catch (error) {
      throw error;
    }
  }
}
