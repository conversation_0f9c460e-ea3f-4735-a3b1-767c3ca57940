import { Injectable } from '@nestjs/common';
import { PriceSetDomain } from './domain/price-set';
import { PriceSetRepository } from './infrastructure/repositories/price-set.repository';
import { PriceSetNotFoundException } from '../../../utils/errors/exceptions/price-set-exceptions';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import {
  AvailabilityType,
  ConfigurationType,
  OffsetType,
} from './domain/price-set.types';
import { toTenantTimezone } from '../utils/date.utils';
import { GetScheduleDto } from './dto/get-schedule.dto';
import { PriceSetAvailabilityDto } from './dto/get-price-set-by-date.dto';
import { PriceModifierDto } from './dto/get-price-modifiers.dto';
import { CustomerDto } from './dto/get-price-customers.dto';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../utils/query-creator/interfaces';
import { AssignModifiersDto } from './dto/assign-modifiers.dto';
import { CreateZoneTableDto } from '../../zone/zone-tables/dto/create-zone-table.dto';
import { GetPriceSetZoneResponseDto } from './dto/get-price-zone.dto';
import { ZoneTableService } from '../../zone/zone-tables/zone-table.service';
import { ZoneTableZoneNotFoundException } from '../../../utils/errors/exceptions/zone-exceptions';
import {
  ECalculationType,
  EModifierGroupBehavior,
  TieredRange,
} from '../price-modifiers/domain/price-modifier.types';

/**
 * Interface for a complete price modifier with all fields
 */
export interface CompletePriceModifier {
  id: string;
  tenantId: string;
  name: string;
  calculationType: ECalculationType;
  fieldName: string;
  applicableRangeMin?: number;
  applicableRangeMax?: number;
  calculationBase?: number;
  increment?: number;
  amount?: number;
  tieredRanges?: TieredRange[];
  tieredDefaultValue: number;
  isActive: boolean;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for a complete price modifier group with all fields
 */
export interface CompletePriceModifierGroup {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  behavior: EModifierGroupBehavior;
  members: CompletePriceModifier[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for a complete price set modifier relationship with all fields
 */
export interface CompletePriceSetModifier {
  id: string;
  priceSetId: string;
  memberId: string;
  isGroup: boolean;
  configuration: ConfigurationType;
  modifier?: CompletePriceModifier;
  group?: CompletePriceModifierGroup;
}

@Injectable()
export class PriceSetsService {
  constructor(
    private readonly priceSetRepository: PriceSetRepository,
    private readonly zoneTableService: ZoneTableService,
  ) {}

  async create(priceSetDomain: PriceSetDomain): Promise<PriceSetDomain> {
    const priceSet = await this.priceSetRepository.create(priceSetDomain);
    return priceSet;
  }

  async duplicatePriceSet(
    priceSetId: PriceSetDomain['id'],
  ): Promise<PriceSetDomain> {
    const duplicatePriceSet =
      await this.priceSetRepository.duplicate(priceSetId);
    return duplicatePriceSet;
  }

  async getPriceSetList(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<PriceSetDomain>> {
    const priceSetDomain = await this.priceSetRepository.find(filter, tenantId);
    return priceSetDomain;
  }

  async getAllPriceSets(tenantId: string): Promise<PriceSetDomain[]> {
    const driverDomain = this.priceSetRepository.findAll(tenantId);
    return driverDomain;
  }

  async getPriceSetsByPickupDate(
    pickupDate: Date,
    tenantId: string,
  ): Promise<Array<PriceSetAvailabilityDto>> {
    // Ensure the date is properly handled with tenant timezone
    const tenantPickupDate = toTenantTimezone(pickupDate).toDate();

    const priceSet = await this.priceSetRepository.findByPickupDate(
      tenantPickupDate,
      tenantId,
    );
    return priceSet;
  }

  async getPriceSetsByPickupDateForAdmin(
    pickupDate: Date,
    tenantId: string,
  ): Promise<Array<PriceSetAvailabilityDto>> {
    // Ensure the date is properly handled with tenant timezone
    const tenantPickupDate = toTenantTimezone(pickupDate).toDate();

    const priceSet = await this.priceSetRepository.findByPickupDateForAdmin(
      tenantPickupDate,
      tenantId,
    );

    return priceSet;
  }

  async getPriceSetsByCustomerId(
    customerId: string,
    tenantId: string,
  ): Promise<PriceSetDomain[]> {
    return this.priceSetRepository.findPriceSetsByCustomerId(
      customerId,
      tenantId,
    );
  }

  async getPriceSetDetails(
    priceSetId: PriceSetDomain['id'],
  ): Promise<PriceSetDomain> {
    const priceSetDomain = await this.priceSetRepository.findOne({
      id: priceSetId,
    });
    if (!priceSetDomain) {
      throw new PriceSetNotFoundException(priceSetId);
    }
    return priceSetDomain;
  }

  async updatePriceSetDetails(priceSetDomain: PriceSetDomain): Promise<void> {
    const priceSet = await this.priceSetRepository.findOne({
      id: priceSetDomain.id,
    });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetDomain.id);
    }

    await this.priceSetRepository.update(priceSetDomain);
    return;
  }

  async deletePriceSet(priceSetId: PriceSetDomain['id']): Promise<void> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    priceSet.isDeleted = true;
    priceSet.deletedAt = new Date();
    await this.priceSetRepository.update(priceSet);
    return;
  }

  async editSchedule(
    priceSetId: PriceSetDomain['id'],
    createScheduleDto: CreateScheduleDto,
  ): Promise<void> {
    this.handleAvailabilityType(createScheduleDto);
    this.handleOffsetType(createScheduleDto);

    await this.priceSetRepository.editSchedule(priceSetId, createScheduleDto);
    return;
  }

  async getScheduleDetails(
    priceSetId: PriceSetDomain['id'],
  ): Promise<GetScheduleDto> {
    const scheduleDomain =
      await this.priceSetRepository.findSchedule(priceSetId);
    if (!scheduleDomain) {
      throw new PriceSetNotFoundException(priceSetId);
    }
    return scheduleDomain;
  }

  async assignPriceModifiers(
    priceSetId: PriceSetDomain['id'],
    assignModifiersDto: AssignModifiersDto,
  ): Promise<void> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    await this.priceSetRepository.assignPriceModifiers(
      priceSetId,
      assignModifiersDto,
    );
    return;
  }

  async getPriceSetModifiers(
    priceSetId: PriceSetDomain['id'],
  ): Promise<PriceModifierDto[]> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    const modifiers =
      await this.priceSetRepository.findModifiersByPriceSetId(priceSetId);
    return modifiers;
  }

  async editConfiguration(
    setModifierId: string,
    configuration: ConfigurationType,
  ): Promise<void> {
    await this.priceSetRepository.editConfiguration(
      setModifierId,
      configuration,
    );
    return;
  }

  async assignCustomers(
    priceSetId: PriceSetDomain['id'],
    customerIds: string[],
  ): Promise<void> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    await this.priceSetRepository.assignCustomers(priceSetId, customerIds);
    return;
  }

  async getPriceSetCustomers(
    priceSetId: PriceSetDomain['id'],
  ): Promise<CustomerDto[]> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    const customers =
      await this.priceSetRepository.findCustomersByPriceSetId(priceSetId);
    return customers;
  }

  async basePriceByZone(
    tenantId: string,
    priceSetId: string,
    createZoneTableDto: CreateZoneTableDto,
  ): Promise<void> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    const zoneIds = new Set<string>();
    createZoneTableDto.zoneTableValues.forEach((value) => {
      zoneIds.add(value.originZoneId);
      zoneIds.add(value.destinationZoneId);
    });

    const zoneValidationResult = await this.zoneTableService.validateZones(
      Array.from(zoneIds),
    );
    if (!zoneValidationResult) {
      throw new ZoneTableZoneNotFoundException();
    }

    await this.priceSetRepository.basePriceByZone(
      tenantId,
      priceSetId,
      createZoneTableDto,
    );
    return;
  }

  async getBasePriceByZone(
    priceSetId: string,
  ): Promise<GetPriceSetZoneResponseDto> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    const zoneTable =
      await this.priceSetRepository.getBasePriceByZone(priceSetId);
    return zoneTable;
  }

  /**
   * Get complete price set modifiers with all data fields as plain objects
   * This method returns all modifier data as flat objects that can be directly used
   *
   * @param priceSetId The ID of the price set
   * @returns Array of plain price modifier objects
   */
  async getCompletePriceSetModifiers(priceSetId: string): Promise<any[]> {
    const priceSet = await this.priceSetRepository.findOne({ id: priceSetId });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    const completeModifiers =
      await this.priceSetRepository.findCompleteModifiersByPriceSetId(
        priceSetId,
      );
    const result: any[] = [];

    // // Process each modifier and flatten the structure
    for (const modifier of completeModifiers) {
      if (modifier.isGroup && modifier.group) {
        this.flattenGroupMembers(
          modifier.group.members || [],
          modifier.group,
          modifier.configuration,
          result,
        );
      } else if (!modifier.isGroup && modifier.modifier) {
        // For individual modifiers, add them directly
        const plainModifier = this.createPlainModifier(
          modifier.modifier.id,
          modifier.modifier.name,
          modifier.modifier.calculationType,
          modifier.modifier.fieldName,
          modifier.modifier.amount || 0,
          modifier.modifier.increment,
          modifier.modifier.calculationBase,
          modifier.modifier.applicableRangeMin,
          modifier.modifier.applicableRangeMax,
          modifier.modifier.tieredRanges,
          modifier.modifier.isActive,
          { configuration: modifier.configuration },
        );
        result.push(plainModifier);
      }
    }
    return result;
  }

  private flattenGroupMembers(
    members: any[],
    parentGroup: any,
    config: ConfigurationType,
    result: any[],
  ) {
    for (const member of members) {
      if (member.isGroup && member.members) {
        // Recurse into the subgroup
        this.flattenGroupMembers(member.members, member, config, result);
      } else {
        // Leaf modifier → create plain object
        const plainModifier = this.createPlainModifier(
          member.id,
          member.name,
          member.calculationType,
          member.fieldName,
          member.amount || 0,
          member.increment,
          member.calculationBase,
          member.applicableRangeMin,
          member.applicableRangeMax,
          member.tieredRanges,
          member.isActive,
          {
            groupId: parentGroup.id,
            groupName: parentGroup.name,
            groupBehavior: parentGroup.behavior,
            configuration: config,
          },
        );
        result.push(plainModifier);
      }
    }
  }

  /**
   * Helper method to create a plain modifier object
   */
  private createPlainModifier(
    id: string,
    name: string,
    calculationType: ECalculationType,
    fieldName: string,
    value: number,
    increment?: number,
    calculationStartAfter?: number,
    applicableRangeMin?: number,
    applicableRangeMax?: number,
    tieredRanges?: any[],
    isEnabled: boolean = true,
    extraProps: Record<string, any> = {},
  ): any {
    // Create the base modifier object
    const modifier: any = {
      id,
      name,
      calculationType,
      calculationField: fieldName,
      value,
      isGroupModifier: false,
      isEnabled,
      ...extraProps,
    };

    // Add optional properties if they exist
    if (increment !== undefined) modifier.increment = increment;

    if (calculationStartAfter !== undefined)
      modifier.calculationStartAfter = calculationStartAfter;

    if (tieredRanges) modifier.tieredRanges = tieredRanges;

    // Add applicable range if min or max exists
    if (applicableRangeMin !== undefined || applicableRangeMax !== undefined) {
      modifier.applicableRange = {};

      if (applicableRangeMin !== undefined) {
        modifier.applicableRange.from = {
          value: applicableRangeMin,
          operator: 'GreaterThanOrEqual',
        };
      }

      if (applicableRangeMax !== undefined) {
        modifier.applicableRange.to = {
          value: applicableRangeMax,
          operator: 'LessThanOrEqual',
        };
      }
    }

    return modifier;
  }

  private handleAvailabilityType(dto: CreateScheduleDto): void {
    if (dto.availabilityType === AvailabilityType.Never) {
      Object.assign(dto, {
        offsetType: null,
        offsetData: null,
        schedule: null,
      });
    } else if (dto.availabilityType === AvailabilityType.Always) {
      dto.schedule = null;
    }
  }

  private handleOffsetType(dto: CreateScheduleDto): void {
    if (!dto.offsetData) return;

    if (dto.offsetType === OffsetType.To) {
      Object.assign(dto.offsetData, {
        time: null,
        daysOut: null,
        includeWeekends: null,
      });
    } else if (dto.offsetType === OffsetType.By) {
      Object.assign(dto.offsetData, { hours: null, minutes: null });
    }
  }
}
