import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PricingService } from './pricing.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';

import {
  GetActivePriceSetsRequestDto,
  GetActivePriceSetsResponseDto,
} from './dto/get-active-price-sets.dto';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import {
  GetAvailableServicesRequestDto,
  GetAvailableServicesResponseDto,
} from '../../customer-portal/pricing/dto/get-available-services.dto';

@ApiTags('Business - Pricing')
@Controller({
  path: 'business/pricing',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard)
export class PricingController {
  constructor(private readonly pricingService: PricingService) {}

  @Post('customers/:customerId/active-price-sets')
  @ApiOperation({
    summary: 'Get all active price sets for a customer with optional pricing',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetActivePriceSetsResponseDto })
  async getActivePriceSets(
    @CurrentUser() userData: JwtPayload,
    @Param('customerId') customerId: string,
    @Body() params: GetActivePriceSetsRequestDto,
  ): Promise<GetActivePriceSetsResponseDto> {
    try {
      if (!userData.ctx.tenantId) {
        throw new Error('User does not have a tenant ID');
      }

      const data = await this.pricingService.getActivePriceSets(
        customerId,
        params,
        userData.ctx.tenantId,
      );

      return { data };
    } catch (error) {
      // Handle specific errors if needed
      throw error;
    }
  }

  @Post('available-services')
  @ApiOperation({
    summary:
      'Get available services based on pickup date with optional pricing',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAvailableServicesResponseDto })
  async getAvailableServices(
    @CurrentUser() userData: JwtPayload,
    @Body() params: GetAvailableServicesRequestDto,
  ): Promise<GetAvailableServicesResponseDto> {
    try {
      if (!userData.ctx.tenantId) {
        throw new Error('User does not have a tenant ID');
      }
      const data = await this.pricingService.getAvailableServicesForAdmin(
        params,
        userData.ctx.tenantId,
      );

      return { data };
    } catch (error) {
      // Handle specific errors if needed
      throw error;
    }
  }
}
