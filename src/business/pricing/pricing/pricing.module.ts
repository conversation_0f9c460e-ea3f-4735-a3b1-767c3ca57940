import { Module } from '@nestjs/common';
import { PricingController } from './pricing.controller';
import { PricingService } from './pricing.service';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { PriceCalculatorModule } from '@core/pricing/price-calculator.module';
import { AddressModule } from '@app/business/customer-portal/address/address.module';
import { RelationalZonePersistenceModule } from '@app/business/zone/zones/infrastructure/relational-persistence.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PriceSetEntity } from '@app/business/pricing/price-sets/infrastructure/entities/price-set.entity';
import { RelationalPriceSetPersistenceModule } from '@app/business/pricing/price-sets/infrastructure/relational-persistence.module';
import { ZoneTableModule } from '@app/business/zone/zone-tables/zone-table.module';
import { RelationalPriceModifierPersistenceModule } from '../price-modifiers/infrastructure/relational-persistence.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PriceSetEntity]),
    RelationalPriceSetPersistenceModule,
    PriceCalculatorModule,
    ZoneTableModule,
    AddressModule,
    RelationalZonePersistenceModule,
    RelationalPriceModifierPersistenceModule,
  ],
  controllers: [PricingController],
  providers: [PricingService, PriceSetsService],
  exports: [PricingService],
})
export class PricingModule {}
