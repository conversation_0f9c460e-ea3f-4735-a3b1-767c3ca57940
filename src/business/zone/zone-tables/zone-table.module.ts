import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { ZoneTableService } from './zone-table.service';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { RelationalZoneTablePersistenceModule } from './infrastructure/relational-persistence.module';
import ZoneTableController from './zone-table.controller';
import { ZoneTableProfile } from './infrastructure/mappers/zone-table.profile';
import { RelationalZonePersistenceModule } from '../zones/infrastructure/relational-persistence.module';

@Module({
  imports: [
    RelationalZoneTablePersistenceModule,
    RelationalZonePersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [ZoneTableController],
  providers: [ZoneTableService, ZoneTableProfile],
  exports: [ZoneTableService, RelationalZoneTablePersistenceModule],
})
export class ZoneTableModule {}
