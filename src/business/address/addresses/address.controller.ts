import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CreateAddressDto, UpdateAddressDto } from './dto/create-address.dto';
import { AddressDomain } from './domain/address';
import { GetAddressDto, GetAllAddressDto } from './dto/get-address.dto';
import { AddressService } from './address.service';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { AddressOperationNotAllowedException } from '../../../utils/errors/exceptions/address-exceptions';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { GetAllAddressesDto } from '../../customer-portal/address/dto/get-all-addresses.dto';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { ContactsService } from '../../user/contacts/contacts.service';
import { GetAllContactsDto } from '../../customer-portal/contact/dto/get-contact.dto';
import { JwtPayload } from '../../../core/auth/domain/auth.types';

@ApiTags('Business - Address')
@Controller({
  path: 'address',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class AddressController {
  constructor(
    private readonly addressService: AddressService,
    private readonly secureFilterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) { }

  @Post()
  @ApiOperation({ summary: 'Create new Address' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createAddressDto: CreateAddressDto,
  ): Promise<AddressDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new AddressOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const addressDomain = this.mapper.map(
        createAddressDto,
        CreateAddressDto,
        AddressDomain,
      );
      addressDomain.tenantId = tenantId;
      addressDomain.createdBy = request.user?.sub;
      addressDomain.updatedBy = request.user?.sub;
      const address = await this.addressService.create(addressDomain);
      return address;
    } catch (error) {
      throw error;
    }
  }

  @Post(':addressId/duplicate')
  @ApiOperation({ summary: 'Duplicate Address by Address Id' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Duplicated' })
  async duplicateAddress(
    @Param('addressId') addressId: string,
  ): Promise<AddressDomain> {
    try {
      const address = await this.addressService.duplicateAddress(addressId);
      return address;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all addresses with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllAddressDto })
  async getAddressList(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllAddressDto> {
    try {
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );

      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new AddressOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getAddressList',
          'Insufficient tenant access permissions',
        );
      }

      const result = await this.addressService.getAddressList(
        combinedFilter,
        tenantId,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        AddressDomain,
        GetAddressDto,
      );

      const response: GetAllAddressDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('customer/:customerId')
  @ApiOperation({
    summary:
      'Get all addresses by customer id with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllAddressDto })
  async getAddressListByCustomerId(
    @Param('customerId') customerId: string,
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllAddressDto> {
    try {
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );
      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new AddressOperationNotAllowedException(
          request.user?.id || 'unknown',
          'create',
          'Insufficient tenant access permissions',
        );
      }

      const result = await this.addressService.getAddressList(
        combinedFilter,
        tenantId,
        customerId,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        AddressDomain,
        GetAddressDto,
      );

      const response: GetAllAddressDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get(':addressId')
  @ApiOperation({ summary: 'Find address by address Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAddressDto })
  async getAddressDetails(
    @Param('addressId') addressId: string,
  ): Promise<GetAddressDto> {
    try {
      const responseDomain =
        await this.addressService.getAddressDetails(addressId);
      const response = this.mapper.map(
        responseDomain,
        AddressDomain,
        GetAddressDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':addressId')
  @ApiOperation({ summary: 'Update address by address Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateAddressDetails(
    @Param('addressId') addressId: string,
    @Body() updateAddressDto: UpdateAddressDto,
    @Req() request: RequestWithUser,
  ): Promise<void> {
    try {
      const address = this.mapper.map(
        updateAddressDto,
        UpdateAddressDto,
        AddressDomain,
      );
      address.id = addressId;
      address.updatedBy = request.user?.sub;
      await this.addressService.updateAddressDetails(address);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':addressId')
  @ApiOperation({ summary: 'Soft-delete address by address Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteAddress(@Param('addressId') addressId: string): Promise<void> {
    try {
      await this.addressService.deleteAddress(addressId);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Get('customer/:customerId/all')
  @ApiOperation({ summary: 'Get all addresses by customer id (no pagination)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: [GetAddressDto] })
  async getAllAddressesByCustomer(
    @Param('customerId') customerId: string,
    @Req() request: RequestWithUser,
  ): Promise<GetAddressDto[]> {
    const tenantId = request.tenantContext?.tenantId;

    if (!tenantId) {
      throw new AddressOperationNotAllowedException(
        request.user?.id || 'unknown',
        'getAllAddressesByCustomer',
        'Insufficient tenant access permissions',
      );
    }

    const addressList = await this.addressService.getAllAddressesByCustomer(
      tenantId,
      customerId,
    );

    return this.mapper.mapArray(addressList, AddressDomain, GetAddressDto);
  }


}
