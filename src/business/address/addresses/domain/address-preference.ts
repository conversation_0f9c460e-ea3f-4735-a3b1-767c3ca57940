import { AutoMap } from '@automapper/classes';
import { ContactEntity } from '../../../user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { AddressEntity } from '../../../address/addresses/infrastructure/entities/address.entity';

export class AddressPreferenceDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  contact: ContactEntity;

  @AutoMap()
  contactId: string;

  @AutoMap()
  address: AddressEntity;

  @AutoMap()
  addressId: string;

  @AutoMap()
  isFavoriteForPickup: boolean;

  @AutoMap()
  isFavoriteForDelivery: boolean;

  @AutoMap()
  isDefaultForPickup: boolean;

  @AutoMap()
  isDefaultForDelivery: boolean;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}
