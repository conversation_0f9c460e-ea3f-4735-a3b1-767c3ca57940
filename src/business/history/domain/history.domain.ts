import { AutoMap } from '@automapper/classes';

export class HistoryDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  entity: string;

  @AutoMap()
  entityId: string;

  @AutoMap()
  property: string;

  @AutoMap()
  propertyDataType: string;

  @AutoMap()
  oldValue: string;

  @AutoMap()
  newValue: string;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;

  @AutoMap()
  createdByName?: string; 

  @AutoMap()
  updatedByName?: string;
}
