import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { EntityRelationalHelper } from '../../../../utils/relational-entity-helper';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';

@Entity('history')
export class HistoryEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column()
  entity: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  entityId: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column()
  property: string;

  @AutoMap()
  @Column()
  propertyDataType: string;

  @AutoMap()
  @Column({ nullable: true })
  oldValue: string;

  @AutoMap()
  @Column({ nullable: true })
  newValue: string;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;


}
