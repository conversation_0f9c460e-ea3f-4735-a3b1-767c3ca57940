import { createMap, forM<PERSON>ber, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { HistoryEntity } from '../entities/history.entity';
import { HistoryDomain } from '../../domain/history.domain';

@Injectable()
export class HistoryProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        HistoryEntity,
        HistoryDomain,
        forMember(
          (dest) => dest.createdByName,
          mapFrom((src: any) => (src as any)['createdByUserName'] || (src as any)['createdByContactName'])
        ),
        forMember(
          (dest) => dest.updatedByName,
          mapFrom((src: any) => (src as any)['updatedByUserName'] || (src as any)['updatedByContactName'])
        ),
      );
    };
  }
}
