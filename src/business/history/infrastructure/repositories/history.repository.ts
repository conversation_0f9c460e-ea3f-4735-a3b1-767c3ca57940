import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { HistoryEntity } from '../entities/history.entity';
import { CreateHistoryDto } from '../../dto/create-history.dto';
import { HistoryDomain } from '../../domain/history.domain';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';

@Injectable()
export class HistoryRepository {
  constructor(
    @InjectRepository(HistoryEntity)
    private readonly historyRepository: Repository<HistoryEntity>,
    @InjectMapper() private readonly mapper: Mapper,
  ) { }

  async create(createDto: CreateHistoryDto): Promise<HistoryEntity> {
    const HistoryEntity = await this.historyRepository.save(
      this.historyRepository.create(createDto),
    );
    return HistoryEntity;
  }


  async find(entity: string, entityId: string): Promise<HistoryDomain[]> {
    const queryBuilder = this.historyRepository
      .createQueryBuilder('history')
      .addSelect(subQuery => {
        return subQuery
          .select('u.contactName')
          .from('users', 'u')
          .where('u.id = history.createdBy');
      }, 'createdByUserName')
      .addSelect(subQuery => {
        return subQuery
          .select('c.name')
          .from('contacts', 'c')
          .where('c.id = history.createdBy');
      }, 'createdByContactName')
      .addSelect(subQuery => {
        return subQuery
          .select('u.contactName')
          .from('users', 'u')
          .where('u.id = history.updatedBy');
      }, 'updatedByUserName')
      .addSelect(subQuery => {
        return subQuery
          .select('c.name')
          .from('contacts', 'c')
          .where('c.id = history.updatedBy');
      }, 'updatedByContactName')
      .where('history.entity = :entity', { entity })
      .andWhere('history.entityId = :entityId', { entityId })
      .orderBy('history.createdAt', 'DESC');

    const { entities, raw } = await queryBuilder.getRawAndEntities();

    const mappedHistories = entities.map((entity, index) => {
      const domain = this.mapper.map(entity, HistoryEntity, HistoryDomain);
      const rawRow = raw[index];

      const createdByName = rawRow['createdByUserName'] || rawRow['createdByContactName'] || null;
      const updatedByName = rawRow['updatedByUserName'] || rawRow['updatedByContactName'] || null;

      return {
        ...domain,
        createdByName,
        updatedByName,
      };
    });

    return mappedHistories;
  }



}
