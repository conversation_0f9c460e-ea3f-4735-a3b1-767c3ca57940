import { Module } from '@nestjs/common';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { HistoryController } from './history.controller';
import { HistoryService } from './history.service';
import { RelationalHistoryPersistenceModule } from './infrastructure/relational-persistence.module';
import { HistoryProfile } from './infrastructure/mappers/history.profile';

@Module({
  imports: [
    RelationalHistoryPersistenceModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [HistoryController],
  providers: [HistoryProfile, HistoryService],
  exports: [HistoryService],
})
export class HistoryModule {}
