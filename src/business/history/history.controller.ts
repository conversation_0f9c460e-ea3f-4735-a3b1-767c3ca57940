import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Req,
} from '@nestjs/common';
import { ApiCreatedResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { HistoryService } from './history.service';
import { CreateHistoryDto } from './dto/create-history.dto';
import { RequestWithUser } from '../../core/auth/interceptors/tenant-context.interceptor';
import { HistoryDomain } from './domain/history.domain';
import { TenantNotFoundException } from '../../utils/errors/exceptions/tenant.exceptions';

@ApiTags('Business - History')
@Controller({
  path: 'history',
  version: '1',
})
export class HistoryController {
  constructor(private readonly historyService: HistoryService) {}

  @Post()
  @ApiOperation({ summary: 'Create a history' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createHistoryDto: CreateHistoryDto,
  ): Promise<HistoryDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new TenantNotFoundException(request.user?.id);
    }
    try {
      const address = await this.historyService.create(createHistoryDto);
      return address;
    } catch (error) {
      throw error;
    }
  }

  @Get(':entity/:entityId')
  @ApiOperation({ summary: 'Find history by entity and entity Id' })
  @HttpCode(HttpStatus.OK)
  @ApiCreatedResponse({ description: 'Created' })
  async find(
    @Param('entity') entity: string,
    @Param('entityId') entityId: string,
  ): Promise<HistoryDomain[]> {
    const history = await this.historyService.find(entity, entityId);
    return history;
  }
}
